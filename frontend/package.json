{"name": "helios-subtitle-corrector", "version": "1.0.0", "description": "AI-powered subtitle correction desktop application", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron dist/main.js", "package": "electron-builder", "package:win": "electron-builder --win", "package:mac": "electron-builder --mac", "package:linux": "electron-builder --linux"}, "keywords": ["subtitle", "correction", "ai", "electron", "typescript"], "author": "Helios Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "electron": "^28.0.0", "electron-builder": "^24.8.1", "eslint": "^8.54.0", "html-webpack-plugin": "^5.5.4", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "style-loader": "^3.3.3", "tailwindcss": "^3.3.6", "ts-loader": "^9.5.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-player": "^2.13.0", "axios": "^1.6.2", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.6"}, "build": {"appId": "com.helios.subtitle-corrector", "productName": "Helios Subtitle Corrector", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.video"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}
{"version": 3, "file": "328.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAsB,CAAC,EAzBZ,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAqB,CAC5BK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAI9B,MAAMR,UAAoBG,EAAaM,UACrC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,mBAAoB,KACtC,MAAME,EAAWF,KAAKG,cACtBH,KAAKI,MAAMC,WAAWH,KAExB3B,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKC,WAAW,YAAY,KAE9B1B,EAAcyB,KAAM,SAAU,KAC5BA,KAAKC,WAAW,YAAY,KAE9B1B,EAAcyB,KAAM,MAAQM,IAC1BN,KAAKM,UAAYA,GAErB,CACA,iBAAAC,GACEP,KAAKI,MAAMI,SAAWR,KAAKI,MAAMI,QAAQR,KAC3C,CACA,IAAAS,CAAKC,GACH,MAAM,SAAEC,EAAQ,OAAEC,EAAM,QAAEC,EAAO,QAAEC,GAAYd,KAAKI,OAC7C,CAAEW,GAAML,EAAIM,MAAMrB,EAAgBsB,uBACrCjB,KAAKkB,OACPlB,KAAKkB,OAAOT,KAAKM,EAAI,CACnBI,OAAO,EAAIzB,EAAa0B,gBAAgBV,GACxCW,SAAUP,KAId,EAAIpB,EAAa4B,QAlCL,+BACG,KACM,cAgC4CC,GAAOA,EAAGL,QAAQM,KAAMD,IACvF,IAAKvB,KAAKM,UACR,OACF,MAAMmB,EAASF,EAAGL,OAClBlB,KAAKkB,OAAS,IAAIO,EAAOzB,KAAKM,UAAW,CACvCoB,MAAO,OACPC,OAAQ,OACRC,MAAOb,EACPc,OAAQ,CACNlB,WACAU,SAAUrB,KAAKI,MAAMU,QACrBgB,KAAM9B,KAAKI,MAAM2B,MACjBZ,OAAO,EAAIzB,EAAa0B,gBAAgBV,GACxCsB,OAAQC,OAAOC,SAASF,UACrBpB,EAAOiB,QAEZM,OAAQ,CACNC,SAAUpC,KAAKI,MAAMiC,QACrBC,OAAQ,IAAMtC,KAAKI,MAAMmC,OAAOvC,KAAKkB,OAAOsB,aAC5CC,UAAWzC,KAAKI,MAAMsC,QACtBC,eAAgB3C,KAAK4C,iBACrBC,MAAO7C,KAAKI,MAAM0C,QAClBhC,QAASd,KAAKI,MAAM2C,OACpBC,QAAShD,KAAKI,MAAM6C,SACpBC,MAAQC,GAAUtC,EAAQsC,OAG7BtC,EACL,CACA,IAAAuC,GACEpD,KAAKC,WAAW,OAClB,CACA,KAAA4C,GACE7C,KAAKC,WAAW,QAClB,CACA,IAAAoD,GACA,CACA,MAAAC,CAAOC,EAASC,GAAc,GAC5BxD,KAAKC,WAAW,OAAQsD,GACnBC,GACHxD,KAAK6C,OAET,CACA,SAAAY,CAAUC,GACR1D,KAAKC,WAAW,YAAayD,EAC/B,CACA,WAAAvD,GACE,OAAOH,KAAKkB,OAAOhB,UAAY,IACjC,CACA,cAAAyD,GACE,OAAO3D,KAAKkB,OAAOsB,WACrB,CACA,gBAAAoB,GACE,OAAO5D,KAAKkB,OAAO2C,YACrB,CACA,MAAAC,GACE,MAAM,QAAEC,GAAY/D,KAAKI,MACnB4D,EAAQ,CACZtC,MAAO,OACPC,OAAQ,OACRoC,WAEF,OAAuBzE,EAAaJ,QAAQ+E,cAAc,MAAO,CAAED,SAAyB1E,EAAaJ,QAAQ+E,cAAc,MAAO,CAAEC,IAAKlE,KAAKkE,MACpJ,EAEF3F,EAAcY,EAAa,cAAe,eAC1CZ,EAAcY,EAAa,UAAWQ,EAAgBwE,QAAQC,aAC9D7F,EAAcY,EAAa,eAAe,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/DailyMotion.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar DailyMotion_exports = {};\n__export(DailyMotion_exports, {\n  default: () => DailyMotion\n});\nmodule.exports = __toCommonJS(DailyMotion_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://api.dmcdn.net/all.js\";\nconst SDK_GLOBAL = \"DM\";\nconst SDK_GLOBAL_READY = \"dmAsyncInit\";\nclass DailyMotion extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { controls, config, onError, playing } = this.props;\n    const [, id] = url.match(import_patterns.MATCH_URL_DAILYMOTION);\n    if (this.player) {\n      this.player.load(id, {\n        start: (0, import_utils.parseStartTime)(url),\n        autoplay: playing\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (DM) => DM.player).then((DM) => {\n      if (!this.container)\n        return;\n      const Player = DM.player;\n      this.player = new Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        video: id,\n        params: {\n          controls,\n          autoplay: this.props.playing,\n          mute: this.props.muted,\n          start: (0, import_utils.parseStartTime)(url),\n          origin: window.location.origin,\n          ...config.params\n        },\n        events: {\n          apiready: this.props.onReady,\n          seeked: () => this.props.onSeek(this.player.currentTime),\n          video_end: this.props.onEnded,\n          durationchange: this.onDurationChange,\n          pause: this.props.onPause,\n          playing: this.props.onPlay,\n          waiting: this.props.onBuffer,\n          error: (event) => onError(event)\n        }\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.player.duration || null;\n  }\n  getCurrentTime() {\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.player.bufferedTime;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(DailyMotion, \"displayName\", \"DailyMotion\");\n__publicField(DailyMotion, \"canPlay\", import_patterns.canPlay.dailymotion);\n__publicField(DailyMotion, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "DailyMotion_exports", "target", "all", "name", "__export", "default", "DailyMotion", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "duration", "getDuration", "props", "onDuration", "container", "componentDidMount", "onMount", "load", "url", "controls", "config", "onError", "playing", "id", "match", "MATCH_URL_DAILYMOTION", "player", "start", "parseStartTime", "autoplay", "getSDK", "DM", "then", "Player", "width", "height", "video", "params", "mute", "muted", "origin", "window", "location", "events", "apiready", "onReady", "seeked", "onSeek", "currentTime", "video_end", "onEnded", "durationchange", "onDurationChange", "pause", "onPause", "onPlay", "waiting", "onBuffer", "error", "event", "play", "stop", "seekTo", "seconds", "keepPlaying", "setVolume", "fraction", "getCurrentTime", "getSecondsLoaded", "bufferedTime", "render", "display", "style", "createElement", "ref", "canPlay", "dailymotion"], "sourceRoot": ""}
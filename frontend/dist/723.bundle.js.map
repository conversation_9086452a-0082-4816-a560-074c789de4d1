{"version": 3, "file": "723.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAc,CAAC,EAzBJ,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAa,CACpBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAkB,EAAQ,KAE9B,MAAMP,UAAYG,EAAaK,UAC7B,WAAAC,GACEC,SAASC,WAETvB,EAAcwB,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMC,WAAWF,IAClEzB,EAAcwB,KAAM,SAAU,IAAIC,IAASD,KAAKE,MAAME,UAAUH,IAChEzB,EAAcwB,KAAM,WAAY,IAAIC,IAASD,KAAKE,MAAMG,YAAYJ,IACpEzB,EAAcwB,KAAM,cAAe,IAAIC,IAASD,KAAKE,MAAMI,eAAeL,IAC1EzB,EAAcwB,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMK,WAAWN,IAClEzB,EAAcwB,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMM,WAAWP,IAClEzB,EAAcwB,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMO,WAAWR,IAClEzB,EAAcwB,KAAM,uBAAyBU,GAAUV,KAAKE,MAAMS,qBAAqBD,EAAM3B,OAAO6B,eACpGpC,EAAcwB,KAAM,cAAe,IAAIC,IAASD,KAAKE,MAAMW,eAAeZ,IAC1EzB,EAAcwB,KAAM,SAAWc,IAC7Bd,KAAKE,MAAMa,OAAOD,EAAE/B,OAAOiC,eAE7BxC,EAAcwB,KAAM,mBAAoB,KACtC,MAAMiB,EAAWjB,KAAKkB,cACtBlB,KAAKE,MAAMiB,WAAWF,KAExBzC,EAAcwB,KAAM,OAAQ,KAC1BA,KAAKoB,OAAOC,OAAQ,IAEtB7C,EAAcwB,KAAM,SAAU,KAC5BA,KAAKoB,OAAOC,OAAQ,IAEtB7C,EAAcwB,KAAM,MAAQoB,IAC1BpB,KAAKoB,OAASA,GAElB,CACA,iBAAAE,GACEtB,KAAKE,MAAMqB,SAAWvB,KAAKE,MAAMqB,QAAQvB,MACzCA,KAAKwB,aAAaxB,KAAKoB,QACvB,MAAMK,EAAazB,KAAK0B,cAAc1B,KAAKE,MAAMyB,KAC7CF,IACFzB,KAAKoB,OAAOK,WAAaA,EAE7B,CACA,oBAAAG,GACE5B,KAAKoB,OAAOK,WAAa,KACzBzB,KAAK6B,gBAAgB7B,KAAKoB,OAC5B,CACA,YAAAI,CAAaJ,GACX,MAAM,YAAEU,GAAgB9B,KAAKE,MAC7BkB,EAAOW,iBAAiB,OAAQ/B,KAAKI,QACrCgB,EAAOW,iBAAiB,UAAW/B,KAAKK,UACxCe,EAAOW,iBAAiB,UAAW/B,KAAKM,aACxCc,EAAOW,iBAAiB,QAAS/B,KAAKO,SACtCa,EAAOW,iBAAiB,SAAU/B,KAAKe,QACvCK,EAAOW,iBAAiB,QAAS/B,KAAKQ,SACtCY,EAAOW,iBAAiB,QAAS/B,KAAKS,SACtCW,EAAOW,iBAAiB,aAAc/B,KAAKgC,sBAC3CZ,EAAOW,iBAAiB,wBAAyB/B,KAAKa,aACtDO,EAAOW,iBAAiB,wBAAyB/B,KAAKiC,cACtDb,EAAOW,iBAAiB,gCAAiC/B,KAAKkC,0BAC9Dd,EAAOW,iBAAiB,UAAW/B,KAAKG,SACpC2B,GACFV,EAAOe,aAAa,cAAe,GAEvC,CACA,eAAAN,CAAgBT,GACdA,EAAOgB,oBAAoB,UAAWpC,KAAKG,SAC3CiB,EAAOgB,oBAAoB,OAAQpC,KAAKI,QACxCgB,EAAOgB,oBAAoB,UAAWpC,KAAKK,UAC3Ce,EAAOgB,oBAAoB,UAAWpC,KAAKM,aAC3Cc,EAAOgB,oBAAoB,QAASpC,KAAKO,SACzCa,EAAOgB,oBAAoB,SAAUpC,KAAKe,QAC1CK,EAAOgB,oBAAoB,QAASpC,KAAKQ,SACzCY,EAAOgB,oBAAoB,QAASpC,KAAKS,SACzCW,EAAOgB,oBAAoB,aAAcpC,KAAKgC,sBAC9CZ,EAAOgB,oBAAoB,wBAAyBpC,KAAKa,aACzDO,EAAOgB,oBAAoB,wBAAyBpC,KAAKiC,cACzDb,EAAOgB,oBAAoB,UAAWpC,KAAKG,QAC7C,CACA,UAAMkC,CAAKV,GACT,IAAIW,EACJ,MAAM,QAAE7B,EAAO,OAAE8B,GAAWvC,KAAKE,MACjC,KAA0C,OAAnCoC,EAAKE,WAAWC,qBAA0B,EAASH,EAAGhE,IAAI,eAC/D,IACE,MAAMoE,EAhFE,2EAgFeC,QAAQ,UAAWJ,EAAOK,eAC3CC,OAEJ,GAAGH,KAEL1C,KAAKE,MAAM4C,UACb,CAAE,MAAOC,GACPtC,EAAQsC,EACV,CAEF,MAAO,CAAEC,GAAMrB,EAAIsB,MAAMtD,EAAgBuD,eACzClD,KAAKoB,OAAOK,WAAauB,CAC3B,CACA,IAAAG,GACE,MAAMC,EAAUpD,KAAKoB,OAAO+B,OACxBC,GACFA,EAAQC,MAAMrD,KAAKE,MAAMO,QAE7B,CACA,KAAA6C,GACEtD,KAAKoB,OAAOkC,OACd,CACA,IAAAC,GACEvD,KAAKoB,OAAOK,WAAa,IAC3B,CACA,MAAA+B,CAAOC,EAASC,GAAc,GAC5B1D,KAAKoB,OAAOJ,YAAcyC,EACrBC,GACH1D,KAAKsD,OAET,CACA,SAAAK,CAAUC,GACR5D,KAAKoB,OAAOyC,OAASD,CACvB,CACA,SAAAE,GACM9D,KAAKoB,OAAO2C,yBAA2BC,SAASC,0BAA4BjE,KAAKoB,QACnFpB,KAAKoB,OAAO2C,yBAEhB,CACA,UAAAG,GACMF,SAASG,sBAAwBH,SAASC,0BAA4BjE,KAAKoB,QAC7E4C,SAASG,sBAEb,CACA,eAAAC,CAAgBC,GACd,IACErE,KAAKoB,OAAOR,aAAeyD,CAC7B,CAAE,MAAOtB,GACP/C,KAAKE,MAAMO,QAAQsC,EACrB,CACF,CACA,WAAA7B,GACE,IAAKlB,KAAKoB,OACR,OAAO,KACT,MAAM,SAAEH,EAAQ,SAAEqD,GAAatE,KAAKoB,OACpC,OAAIH,IAAasD,KAAYD,EAASE,OAAS,EACtCF,EAASG,IAAIH,EAASE,OAAS,GAEjCvD,CACT,CACA,cAAAyD,GACE,OAAK1E,KAAKoB,OAEHpB,KAAKoB,OAAOJ,YADV,IAEX,CACA,gBAAA2D,GACE,IAAK3E,KAAKoB,OACR,OAAO,KACT,MAAM,SAAEwD,GAAa5E,KAAKoB,OAC1B,GAAwB,IAApBwD,EAASJ,OACX,OAAO,EAET,MAAMC,EAAMG,EAASH,IAAIG,EAASJ,OAAS,GACrCvD,EAAWjB,KAAKkB,cACtB,OAAIuD,EAAMxD,EACDA,EAEFwD,CACT,CACA,aAAA/C,CAAcC,GACZ,MAAO,CAAEqB,GAAMrB,EAAIsB,MAAMtD,EAAgBuD,eACzC,OAAOF,CACT,CACA,MAAA6B,GACE,MAAM,IAAElD,EAAG,QAAEmD,EAAO,KAAEC,EAAI,SAAEC,EAAQ,MAAE3D,EAAK,OAAEkB,EAAM,MAAE0C,EAAK,OAAEC,GAAWlF,KAAKE,MACtEiF,EAAQ,CACZF,MAAiB,SAAVA,EAAmBA,EAAQ,OAClCC,OAAmB,SAAXA,EAAoBA,EAAS,QAKvC,OAHiB,IAAbF,IACFG,EAAM,cAAgB,QAED5F,EAAaJ,QAAQiG,cAC1C,aACA,CACEC,IAAKrF,KAAKqF,IACV,cAAerF,KAAK0B,cAAcC,GAClCwD,QACAG,QAAS,OACTC,SAAUT,QAAW,EACrBzD,MAAOA,EAAQ,QAAK,EACpB0D,KAAMA,EAAO,QAAK,KACfxC,EAAOiD,YAGhB,EAEFhH,EAAcY,EAAK,cAAe,OAClCZ,EAAcY,EAAK,UAAWO,EAAgB8F,QAAQC,I", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Mux.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mux_exports = {};\n__export(Mux_exports, {\n  default: () => Mux\n});\nmodule.exports = __toCommonJS(Mux_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.jsdelivr.net/npm/@mux/mux-player@VERSION/dist/mux-player.mjs\";\nclass Mux extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"ref\", (player) => {\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const playbackId = this.getPlaybackId(this.props.url);\n    if (playbackId) {\n      this.player.playbackId = playbackId;\n    }\n  }\n  componentWillUnmount() {\n    this.player.playbackId = null;\n    this.removeListeners(this.player);\n  }\n  addListeners(player) {\n    const { playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    player.addEventListener(\"canplay\", this.onReady);\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n    }\n  }\n  removeListeners(player) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"canplay\", this.onReady);\n  }\n  async load(url) {\n    var _a;\n    const { onError, config } = this.props;\n    if (!((_a = globalThis.customElements) == null ? void 0 : _a.get(\"mux-player\"))) {\n      try {\n        const sdkUrl = SDK_URL.replace(\"VERSION\", config.version);\n        await import(\n          /* webpackIgnore: true */\n          `${sdkUrl}`\n        );\n        this.props.onLoaded();\n      } catch (error) {\n        onError(error);\n      }\n    }\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    this.player.playbackId = id;\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.playbackId = null;\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getPlaybackId(url) {\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    return id;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    if (controls === false) {\n      style[\"--controls\"] = \"none\";\n    }\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"mux-player\",\n      {\n        ref: this.ref,\n        \"playback-id\": this.getPlaybackId(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        muted: muted ? \"\" : void 0,\n        loop: loop ? \"\" : void 0,\n        ...config.attributes\n      }\n    );\n  }\n}\n__publicField(Mux, \"displayName\", \"Mux\");\n__publicField(Mux, \"canPlay\", import_patterns.canPlay.mux);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Mux_exports", "target", "all", "name", "__export", "default", "<PERSON><PERSON>", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_patterns", "Component", "constructor", "super", "arguments", "this", "args", "props", "onReady", "onPlay", "onBuffer", "onBufferEnd", "onPause", "onEnded", "onError", "event", "onPlaybackRateChange", "playbackRate", "onEnablePIP", "e", "onSeek", "currentTime", "duration", "getDuration", "onDuration", "player", "muted", "componentDidMount", "onMount", "addListeners", "playbackId", "getPlaybackId", "url", "componentWillUnmount", "removeListeners", "playsinline", "addEventListener", "onPlayBackRateChange", "onDisablePIP", "onPresentationModeChange", "setAttribute", "removeEventListener", "load", "_a", "config", "globalThis", "customElements", "sdkUrl", "replace", "version", "import", "onLoaded", "error", "id", "match", "MATCH_URL_MUX", "play", "promise", "catch", "pause", "stop", "seekTo", "seconds", "keepPlaying", "setVolume", "fraction", "volume", "enablePIP", "requestPictureInPicture", "document", "pictureInPictureElement", "disablePIP", "exitPictureInPicture", "setPlaybackRate", "rate", "seekable", "Infinity", "length", "end", "getCurrentTime", "getSecondsLoaded", "buffered", "render", "playing", "loop", "controls", "width", "height", "style", "createElement", "ref", "preload", "autoPlay", "attributes", "canPlay", "mux"], "sourceRoot": ""}
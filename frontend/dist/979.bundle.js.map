{"version": 3, "file": "979.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBX,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAoB,CAC3BK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAG9B,MAAMR,UAAmBG,EAAaM,UACpC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAY,MAChCzB,EAAcyB,KAAM,cAAe,MACnCzB,EAAcyB,KAAM,iBAAkB,MACtCzB,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKE,UAAU,KAEjB3B,EAAcyB,KAAM,SAAU,KACF,OAAtBA,KAAKG,MAAMC,QACbJ,KAAKE,UAAUF,KAAKG,MAAMC,UAG9B7B,EAAcyB,KAAM,MAAQK,IAC1BL,KAAKK,OAASA,GAElB,CACA,iBAAAC,GACEN,KAAKG,MAAMI,SAAWP,KAAKG,MAAMI,QAAQP,KAC3C,CACA,IAAAQ,CAAKC,EAAKC,IACR,EAAIhB,EAAaiB,QAzBL,yCACG,MAwB+BC,KAAMC,IAClD,IAAKb,KAAKK,OACR,OACF,MAAM,KAAES,EAAI,cAAEC,EAAa,MAAEC,EAAK,OAAEC,EAAM,MAAEC,GAAUL,EAAGM,OAAOC,OAC3DV,IACHV,KAAKqB,OAASR,EAAGM,OAAOnB,KAAKK,QAC7BL,KAAKqB,OAAOC,KAAKR,EAAMd,KAAKG,MAAMoB,QAClCvB,KAAKqB,OAAOC,KAAKN,EAAO,KACJhB,KAAKwB,SAAWxB,KAAKyB,YACvB,KAGhBzB,KAAKG,MAAMuB,YAEb1B,KAAKqB,OAAOC,KAAKP,EAAgBY,IAC/B3B,KAAKyB,YAAcE,EAAEC,gBAAkB,IACvC5B,KAAK6B,eAAiBF,EAAEG,iBAE1B9B,KAAKqB,OAAOC,KAAKL,EAAQ,IAAMjB,KAAKG,MAAM4B,WAC1C/B,KAAKqB,OAAOC,KAAKJ,EAAQS,GAAM3B,KAAKG,MAAM6B,QAAQL,KAEpD3B,KAAKqB,OAAOb,KAAKC,EAAK,IACjBT,KAAKG,MAAM8B,OAAOC,QACrBC,SAAU,KACRnC,KAAKqB,OAAOe,YAAaZ,IACvBxB,KAAKwB,SAAWA,EAAW,IAC3BxB,KAAKG,MAAMkC,gBAKrB,CACA,IAAAC,GACEtC,KAAKC,WAAW,OAClB,CACA,KAAAsC,GACEvC,KAAKC,WAAW,QAClB,CACA,IAAAuC,GACA,CACA,MAAAC,CAAOC,EAASC,GAAc,GAC5B3C,KAAKC,WAAW,SAAoB,IAAVyC,GACrBC,GACH3C,KAAKuC,OAET,CACA,SAAArC,CAAU0C,GACR5C,KAAKC,WAAW,YAAwB,IAAX2C,EAC/B,CACA,WAAAR,GACE,OAAOpC,KAAKwB,QACd,CACA,cAAAqB,GACE,OAAO7C,KAAKyB,WACd,CACA,gBAAAqB,GACE,OAAO9C,KAAK6B,eAAiB7B,KAAKwB,QACpC,CACA,MAAAuB,GACE,MAAM,QAAEC,GAAYhD,KAAKG,MACnB8C,EAAQ,CACZC,MAAO,OACPC,OAAQ,OACRH,WAEF,OAAuB1D,EAAaJ,QAAQkE,cAC1C,SACA,CACEC,IAAKrD,KAAKqD,IACVC,IAAK,wCAAwCC,mBAAmBvD,KAAKG,MAAMM,OAC3EwC,QACAO,YAAa,EACbC,MAAO,YAGb,EAEFlF,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWQ,EAAgB+D,QAAQC,YAC7DpF,EAAcY,EAAY,eAAe,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/SoundCloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar SoundCloud_exports = {};\n__export(SoundCloud_exports, {\n  default: () => SoundCloud\n});\nmodule.exports = __toCommonJS(SoundCloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://w.soundcloud.com/player/api.js\";\nconst SDK_GLOBAL = \"SC\";\nclass SoundCloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"fractionLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((SC) => {\n      if (!this.iframe)\n        return;\n      const { PLAY, PLAY_PROGRESS, PAUSE, FINISH, ERROR } = SC.Widget.Events;\n      if (!isReady) {\n        this.player = SC.Widget(this.iframe);\n        this.player.bind(PLAY, this.props.onPlay);\n        this.player.bind(PAUSE, () => {\n          const remaining = this.duration - this.currentTime;\n          if (remaining < 0.05) {\n            return;\n          }\n          this.props.onPause();\n        });\n        this.player.bind(PLAY_PROGRESS, (e) => {\n          this.currentTime = e.currentPosition / 1e3;\n          this.fractionLoaded = e.loadedProgress;\n        });\n        this.player.bind(FINISH, () => this.props.onEnded());\n        this.player.bind(ERROR, (e) => this.props.onError(e));\n      }\n      this.player.load(url, {\n        ...this.props.config.options,\n        callback: () => {\n          this.player.getDuration((duration) => {\n            this.duration = duration / 1e3;\n            this.props.onReady();\n          });\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seekTo\", seconds * 1e3);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.fractionLoaded * this.duration;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: `https://w.soundcloud.com/player/?url=${encodeURIComponent(this.props.url)}`,\n        style,\n        frameBorder: 0,\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(SoundCloud, \"displayName\", \"SoundCloud\");\n__publicField(SoundCloud, \"canPlay\", import_patterns.canPlay.soundcloud);\n__publicField(SoundCloud, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "SoundCloud_exports", "target", "all", "name", "__export", "default", "SoundCloud", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "setVolume", "props", "volume", "iframe", "componentDidMount", "onMount", "load", "url", "isReady", "getSDK", "then", "SC", "PLAY", "PLAY_PROGRESS", "PAUSE", "FINISH", "ERROR", "Widget", "Events", "player", "bind", "onPlay", "duration", "currentTime", "onPause", "e", "currentPosition", "fractionLoaded", "loadedProgress", "onEnded", "onError", "config", "options", "callback", "getDuration", "onReady", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "fraction", "getCurrentTime", "getSecondsLoaded", "render", "display", "style", "width", "height", "createElement", "ref", "src", "encodeURIComponent", "frameBorder", "allow", "canPlay", "soundcloud"], "sourceRoot": ""}
{"version": 3, "file": "463.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBR,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAiB,CACxBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAG9B,MAAMR,UAAgBG,EAAaM,UACjC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAY,MAChCzB,EAAcyB,KAAM,cAAe,MACnCzB,EAAcyB,KAAM,gBAAiB,MACrCzB,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKC,WAAW,UAElB1B,EAAcyB,KAAM,SAAU,KAC5BA,KAAKC,WAAW,YAElB1B,EAAcyB,KAAM,MAAQE,IAC1BF,KAAKE,OAASA,GAElB,CACA,iBAAAC,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACA,IAAAM,CAAKC,IACH,EAAIb,EAAac,QAvBL,2CACG,YAsB+BC,KAAMC,IAC7CV,KAAKE,SAEVF,KAAKW,OAAS,IAAID,EAASE,OAAOZ,KAAKE,QACvCF,KAAKW,OAAOE,GAAG,QAAS,KACtBC,WAAW,KACTd,KAAKW,OAAOI,SAAU,EACtBf,KAAKW,OAAOK,QAAQhB,KAAKI,MAAMa,MAC3BjB,KAAKI,MAAMc,OACblB,KAAKW,OAAOQ,OAEdnB,KAAKoB,aAAapB,KAAKW,OAAQX,KAAKI,OACpCJ,KAAKI,MAAMiB,WACV,SAEJrB,KAAKI,MAAMkB,QAChB,CACA,YAAAF,CAAaT,EAAQP,GACnBO,EAAOE,GAAG,OAAQT,EAAMmB,QACxBZ,EAAOE,GAAG,QAAST,EAAMoB,SACzBb,EAAOE,GAAG,QAAST,EAAMqB,SACzBd,EAAOE,GAAG,QAAST,EAAMkB,SACzBX,EAAOE,GAAG,aAAc,EAAGa,WAAUC,cACnC3B,KAAK0B,SAAWA,EAChB1B,KAAK4B,YAAcD,GAEvB,CACA,IAAAE,GACE7B,KAAKC,WAAW,OAClB,CACA,KAAA6B,GACE9B,KAAKC,WAAW,QAClB,CACA,IAAA8B,GACA,CACA,MAAAC,CAAOL,EAASM,GAAc,GAC5BjC,KAAKC,WAAW,iBAAkB0B,GAC7BM,GACHjC,KAAK8B,OAET,CACA,SAAAI,CAAUC,GACRnC,KAAKC,WAAW,YAAakC,EAC/B,CACA,OAAAnB,CAAQC,GACNjB,KAAKC,WAAW,UAAWgB,EAC7B,CACA,WAAAmB,GACE,OAAOpC,KAAK0B,QACd,CACA,cAAAW,GACE,OAAOrC,KAAK4B,WACd,CACA,gBAAAU,GACE,OAAOtC,KAAKuC,aACd,CACA,MAAAC,GAKE,OAAuBlD,EAAaJ,QAAQuD,cAC1C,SACA,CACEC,IAAK1C,KAAK0C,IACVC,IAAK3C,KAAKI,MAAMG,IAChBqC,YAAa,IACbC,UAAW,KACXC,MAXU,CACZC,MAAO,OACPC,OAAQ,QAUNC,MAAO,yCACPC,eAAgB,8BAGtB,EAEF3E,EAAcY,EAAS,cAAe,WACtCZ,EAAcY,EAAS,UAAWQ,EAAgBwD,QAAQC,Q", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Kaltura.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Kaltura_exports = {};\n__export(Kaltura_exports, {\n  default: () => Kaltura\n});\nmodule.exports = __toCommonJS(Kaltura_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.embed.ly/player-0.1.0.min.js\";\nconst SDK_GLOBAL = \"playerjs\";\nclass Kaltura extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((playerjs) => {\n      if (!this.iframe)\n        return;\n      this.player = new playerjs.Player(this.iframe);\n      this.player.on(\"ready\", () => {\n        setTimeout(() => {\n          this.player.isReady = true;\n          this.player.setLoop(this.props.loop);\n          if (this.props.muted) {\n            this.player.mute();\n          }\n          this.addListeners(this.player, this.props);\n          this.props.onReady();\n        }, 500);\n      });\n    }, this.props.onError);\n  }\n  addListeners(player, props) {\n    player.on(\"play\", props.onPlay);\n    player.on(\"pause\", props.onPause);\n    player.on(\"ended\", props.onEnded);\n    player.on(\"error\", props.onError);\n    player.on(\"timeupdate\", ({ duration, seconds }) => {\n      this.duration = duration;\n      this.currentTime = seconds;\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: this.props.url,\n        frameBorder: \"0\",\n        scrolling: \"no\",\n        style,\n        allow: \"encrypted-media; autoplay; fullscreen;\",\n        referrerPolicy: \"no-referrer-when-downgrade\"\n      }\n    );\n  }\n}\n__publicField(Kaltura, \"displayName\", \"Kaltura\");\n__publicField(Kaltura, \"canPlay\", import_patterns.canPlay.kaltura);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Kaltura_exports", "target", "all", "name", "__export", "default", "<PERSON><PERSON><PERSON>", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "playerjs", "player", "Player", "on", "setTimeout", "isReady", "setLoop", "loop", "muted", "mute", "addListeners", "onReady", "onError", "onPlay", "onPause", "onEnded", "duration", "seconds", "currentTime", "play", "pause", "stop", "seekTo", "keepPlaying", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "secondsLoaded", "render", "createElement", "ref", "src", "frameBorder", "scrolling", "style", "width", "height", "allow", "referrerPolicy", "canPlay", "kaltura"], "sourceRoot": ""}
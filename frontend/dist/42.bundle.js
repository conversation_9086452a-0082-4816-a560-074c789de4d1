(global.webpackChunkhelios_subtitle_corrector=global.webpackChunkhelios_subtitle_corrector||[]).push([[42],{1400:(e,t,r)=>{var a,s=Object.create,l=Object.defineProperty,o=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,h=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of i(t))p.call(e,s)||s===r||l(e,s,{get:()=>t[s],enumerable:!(a=o(t,s))||a.enumerable});return e},c=(e,t,r)=>(((e,t,r)=>{t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r),u={};((e,t)=>{for(var r in t)l(e,r,{get:t[r],enumerable:!0})})(u,{default:()=>P}),e.exports=(a=u,h(l({},"__esModule",{value:!0}),a));var y=((e,t,r)=>(r=null!=e?s(n(e)):{},h(e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)))(r(6540)),d=r(5635),m=r(327);class P extends y.Component{constructor(){super(...arguments),c(this,"callPlayer",d.callPlayer),c(this,"playerID",this.props.config.playerId||`twitch-player-${(0,d.randomString)()}`),c(this,"mute",()=>{this.callPlayer("setMuted",!0)}),c(this,"unmute",()=>{this.callPlayer("setMuted",!1)})}componentDidMount(){this.props.onMount&&this.props.onMount(this)}load(e,t){const{playsinline:r,onError:a,config:s,controls:l}=this.props,o=m.MATCH_URL_TWITCH_CHANNEL.test(e),i=o?e.match(m.MATCH_URL_TWITCH_CHANNEL)[1]:e.match(m.MATCH_URL_TWITCH_VIDEO)[1];t?o?this.player.setChannel(i):this.player.setVideo("v"+i):(0,d.getSDK)("https://player.twitch.tv/js/embed/v1.js","Twitch").then(t=>{this.player=new t.Player(this.playerID,{video:o?"":i,channel:o?i:"",height:"100%",width:"100%",playsinline:r,autoplay:this.props.playing,muted:this.props.muted,controls:!!o||l,time:(0,d.parseStartTime)(e),...s.options});const{READY:a,PLAYING:n,PAUSE:p,ENDED:h,ONLINE:c,OFFLINE:u,SEEK:y}=t.Player;this.player.addEventListener(a,this.props.onReady),this.player.addEventListener(n,this.props.onPlay),this.player.addEventListener(p,this.props.onPause),this.player.addEventListener(h,this.props.onEnded),this.player.addEventListener(y,this.props.onSeek),this.player.addEventListener(c,this.props.onLoaded),this.player.addEventListener(u,this.props.onLoaded)},a)}play(){this.callPlayer("play")}pause(){this.callPlayer("pause")}stop(){this.callPlayer("pause")}seekTo(e,t=!0){this.callPlayer("seek",e),t||this.pause()}setVolume(e){this.callPlayer("setVolume",e)}getDuration(){return this.callPlayer("getDuration")}getCurrentTime(){return this.callPlayer("getCurrentTime")}getSecondsLoaded(){return null}render(){return y.default.createElement("div",{style:{width:"100%",height:"100%"},id:this.playerID})}}c(P,"displayName","Twitch"),c(P,"canPlay",m.canPlay.twitch),c(P,"loopOnEnded",!0)}}]);
//# sourceMappingURL=42.bundle.js.map
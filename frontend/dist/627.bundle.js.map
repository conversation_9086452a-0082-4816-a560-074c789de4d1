{"version": 3, "file": "627.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBX,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAoB,CAC3BK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAG9B,MAAMR,UAAmBG,EAAaM,UACpC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAY,MAChCzB,EAAcyB,KAAM,cAAe,MACnCzB,EAAcyB,KAAM,gBAAiB,MACrCzB,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKC,WAAW,UAElB1B,EAAcyB,KAAM,SAAU,KAC5BA,KAAKC,WAAW,YAElB1B,EAAcyB,KAAM,MAAQE,IAC1BF,KAAKE,OAASA,GAElB,CACA,iBAAAC,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACA,IAAAM,CAAKC,IACH,EAAIb,EAAac,QAvBL,2CACG,YAsB+BC,KAAMC,IAC7CV,KAAKE,SAEVF,KAAKW,OAAS,IAAID,EAASE,OAAOZ,KAAKE,QACvCF,KAAKW,OAAOE,QAAQb,KAAKI,MAAMU,MAC/Bd,KAAKW,OAAOI,GAAG,QAASf,KAAKI,MAAMY,SACnChB,KAAKW,OAAOI,GAAG,OAAQf,KAAKI,MAAMa,QAClCjB,KAAKW,OAAOI,GAAG,QAASf,KAAKI,MAAMc,SACnClB,KAAKW,OAAOI,GAAG,SAAUf,KAAKI,MAAMe,QACpCnB,KAAKW,OAAOI,GAAG,QAASf,KAAKI,MAAMgB,SACnCpB,KAAKW,OAAOI,GAAG,QAASf,KAAKI,MAAMiB,SACnCrB,KAAKW,OAAOI,GAAG,aAAc,EAAGO,WAAUC,cACxCvB,KAAKsB,SAAWA,EAChBtB,KAAKwB,YAAcD,IAErBvB,KAAKW,OAAOI,GAAG,WAAY,EAAGU,cACxBzB,KAAKsB,WACPtB,KAAK0B,cAAgB1B,KAAKsB,SAAWG,KAGrCzB,KAAKI,MAAMuB,OACb3B,KAAKW,OAAOiB,SAEb5B,KAAKI,MAAMiB,QAChB,CACA,IAAAQ,GACE7B,KAAKC,WAAW,OAClB,CACA,KAAA6B,GACE9B,KAAKC,WAAW,QAClB,CACA,IAAA8B,GACA,CACA,MAAAC,CAAOT,EAASU,GAAc,GAC5BjC,KAAKC,WAAW,iBAAkBsB,GAC7BU,GACHjC,KAAK8B,OAET,CACA,SAAAI,CAAUC,GACRnC,KAAKC,WAAW,YAAwB,IAAXkC,EAC/B,CACA,OAAAtB,CAAQC,GACNd,KAAKC,WAAW,UAAWa,EAC7B,CACA,WAAAsB,GACE,OAAOpC,KAAKsB,QACd,CACA,cAAAe,GACE,OAAOrC,KAAKwB,WACd,CACA,gBAAAc,GACE,OAAOtC,KAAK0B,aACd,CACA,MAAAa,GACE,MAAMC,EAAKxC,KAAKI,MAAMG,IAAIkC,MAAM9C,EAAgB+C,sBAAsB,GAKtE,OAAuBpD,EAAaJ,QAAQyD,cAC1C,SACA,CACEC,IAAK5C,KAAK4C,IACVC,IAAK,4BAA4BL,IACjCM,YAAa,IACbC,UAAW,KACXC,MAXU,CACZC,MAAO,OACPC,OAAQ,QAUNC,MAAO,0CAGb,EAEF5E,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWQ,EAAgByD,QAAQC,W", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Streamable.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Streamable_exports = {};\n__export(Streamable_exports, {\n  default: () => Streamable\n});\nmodule.exports = __toCommonJS(Streamable_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.embed.ly/player-0.1.0.min.js\";\nconst SDK_GLOBAL = \"playerjs\";\nclass Streamable extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((playerjs) => {\n      if (!this.iframe)\n        return;\n      this.player = new playerjs.Player(this.iframe);\n      this.player.setLoop(this.props.loop);\n      this.player.on(\"ready\", this.props.onReady);\n      this.player.on(\"play\", this.props.onPlay);\n      this.player.on(\"pause\", this.props.onPause);\n      this.player.on(\"seeked\", this.props.onSeek);\n      this.player.on(\"ended\", this.props.onEnded);\n      this.player.on(\"error\", this.props.onError);\n      this.player.on(\"timeupdate\", ({ duration, seconds }) => {\n        this.duration = duration;\n        this.currentTime = seconds;\n      });\n      this.player.on(\"buffered\", ({ percent }) => {\n        if (this.duration) {\n          this.secondsLoaded = this.duration * percent;\n        }\n      });\n      if (this.props.muted) {\n        this.player.mute();\n      }\n    }, this.props.onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const id = this.props.url.match(import_patterns.MATCH_URL_STREAMABLE)[1];\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: `https://streamable.com/o/${id}`,\n        frameBorder: \"0\",\n        scrolling: \"no\",\n        style,\n        allow: \"encrypted-media; autoplay; fullscreen;\"\n      }\n    );\n  }\n}\n__publicField(Streamable, \"displayName\", \"Streamable\");\n__publicField(Streamable, \"canPlay\", import_patterns.canPlay.streamable);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Streamable_exports", "target", "all", "name", "__export", "default", "Streamable", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "playerjs", "player", "Player", "setLoop", "loop", "on", "onReady", "onPlay", "onPause", "onSeek", "onEnded", "onError", "duration", "seconds", "currentTime", "percent", "secondsLoaded", "muted", "mute", "play", "pause", "stop", "seekTo", "keepPlaying", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "id", "match", "MATCH_URL_STREAMABLE", "createElement", "ref", "src", "frameBorder", "scrolling", "style", "width", "height", "allow", "canPlay", "streamable"], "sourceRoot": ""}
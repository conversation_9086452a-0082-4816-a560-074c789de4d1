{"version": 3, "file": "42.bundle.js", "mappings": "2HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAiB,CAAC,EAzBP,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAgB,CACvBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAI9B,MAAMR,UAAeG,EAAaM,UAChC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAYA,KAAKE,MAAMC,OAAOC,UAAY,kBAAsB,EAAIV,EAAaW,mBACrG9B,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKC,WAAW,YAAY,KAE9B1B,EAAcyB,KAAM,SAAU,KAC5BA,KAAKC,WAAW,YAAY,IAEhC,CACA,iBAAAK,GACEN,KAAKE,MAAMK,SAAWP,KAAKE,MAAMK,QAAQP,KAC3C,CACA,IAAAQ,CAAKC,EAAKC,GACR,MAAM,YAAEC,EAAW,QAAEC,EAAO,OAAET,EAAM,SAAEU,GAAab,KAAKE,MAClDY,EAAYnB,EAAgBoB,yBAAyBC,KAAKP,GAC1DQ,EAAKH,EAAYL,EAAIS,MAAMvB,EAAgBoB,0BAA0B,GAAKN,EAAIS,MAAMvB,EAAgBwB,wBAAwB,GAC9HT,EACEI,EACFd,KAAKoB,OAAOC,WAAWJ,GAEvBjB,KAAKoB,OAAOE,SAAS,IAAML,IAI/B,EAAIvB,EAAa6B,QA9BL,0CACG,UA6B+BC,KAAMC,IAClDzB,KAAKoB,OAAS,IAAIK,EAAQC,OAAO1B,KAAK2B,SAAU,CAC9CC,MAAOd,EAAY,GAAKG,EACxBY,QAASf,EAAYG,EAAK,GAC1Ba,OAAQ,OACRC,MAAO,OACPpB,cACAqB,SAAUhC,KAAKE,MAAM+B,QACrBC,MAAOlC,KAAKE,MAAMgC,MAElBrB,WAAUC,GAAmBD,EAC7BsB,MAAM,EAAIzC,EAAa0C,gBAAgB3B,MACpCN,EAAOkC,UAEZ,MAAM,MAAEC,EAAK,QAAEC,EAAO,MAAEC,EAAK,MAAEC,EAAK,OAAEC,EAAM,QAAEC,EAAO,KAAEC,GAASnB,EAAQC,OACxE1B,KAAKoB,OAAOyB,iBAAiBP,EAAOtC,KAAKE,MAAM4C,SAC/C9C,KAAKoB,OAAOyB,iBAAiBN,EAASvC,KAAKE,MAAM6C,QACjD/C,KAAKoB,OAAOyB,iBAAiBL,EAAOxC,KAAKE,MAAM8C,SAC/ChD,KAAKoB,OAAOyB,iBAAiBJ,EAAOzC,KAAKE,MAAM+C,SAC/CjD,KAAKoB,OAAOyB,iBAAiBD,EAAM5C,KAAKE,MAAMgD,QAC9ClD,KAAKoB,OAAOyB,iBAAiBH,EAAQ1C,KAAKE,MAAMiD,UAChDnD,KAAKoB,OAAOyB,iBAAiBF,EAAS3C,KAAKE,MAAMiD,WAChDvC,EACL,CACA,IAAAwC,GACEpD,KAAKC,WAAW,OAClB,CACA,KAAAoD,GACErD,KAAKC,WAAW,QAClB,CACA,IAAAqD,GACEtD,KAAKC,WAAW,QAClB,CACA,MAAAsD,CAAOC,EAASC,GAAc,GAC5BzD,KAAKC,WAAW,OAAQuD,GACnBC,GACHzD,KAAKqD,OAET,CACA,SAAAK,CAAUC,GACR3D,KAAKC,WAAW,YAAa0D,EAC/B,CACA,WAAAC,GACE,OAAO5D,KAAKC,WAAW,cACzB,CACA,cAAA4D,GACE,OAAO7D,KAAKC,WAAW,iBACzB,CACA,gBAAA6D,GACE,OAAO,IACT,CACA,MAAAC,GAKE,OAAuBzE,EAAaJ,QAAQ8E,cAAc,MAAO,CAAEC,MAJrD,CACZlC,MAAO,OACPD,OAAQ,QAEgEb,GAAIjB,KAAK2B,UACrF,EAEFpD,EAAcY,EAAQ,cAAe,UACrCZ,EAAcY,EAAQ,UAAWQ,EAAgBuE,QAAQC,QACzD5F,EAAcY,EAAQ,eAAe,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Twitch.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Twitch_exports = {};\n__export(Twitch_exports, {\n  default: () => Twitch\n});\nmodule.exports = __toCommonJS(Twitch_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.twitch.tv/js/embed/v1.js\";\nconst SDK_GLOBAL = \"Twitch\";\nconst PLAYER_ID_PREFIX = \"twitch-player-\";\nclass Twitch extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    const { playsinline, onError, config, controls } = this.props;\n    const isChannel = import_patterns.MATCH_URL_TWITCH_CHANNEL.test(url);\n    const id = isChannel ? url.match(import_patterns.MATCH_URL_TWITCH_CHANNEL)[1] : url.match(import_patterns.MATCH_URL_TWITCH_VIDEO)[1];\n    if (isReady) {\n      if (isChannel) {\n        this.player.setChannel(id);\n      } else {\n        this.player.setVideo(\"v\" + id);\n      }\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Twitch2) => {\n      this.player = new Twitch2.Player(this.playerID, {\n        video: isChannel ? \"\" : id,\n        channel: isChannel ? id : \"\",\n        height: \"100%\",\n        width: \"100%\",\n        playsinline,\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        // https://github.com/CookPete/react-player/issues/733#issuecomment-549085859\n        controls: isChannel ? true : controls,\n        time: (0, import_utils.parseStartTime)(url),\n        ...config.options\n      });\n      const { READY, PLAYING, PAUSE, ENDED, ONLINE, OFFLINE, SEEK } = Twitch2.Player;\n      this.player.addEventListener(READY, this.props.onReady);\n      this.player.addEventListener(PLAYING, this.props.onPlay);\n      this.player.addEventListener(PAUSE, this.props.onPause);\n      this.player.addEventListener(ENDED, this.props.onEnded);\n      this.player.addEventListener(SEEK, this.props.onSeek);\n      this.player.addEventListener(ONLINE, this.props.onLoaded);\n      this.player.addEventListener(OFFLINE, this.props.onLoaded);\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"pause\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style, id: this.playerID });\n  }\n}\n__publicField(Twitch, \"displayName\", \"Twitch\");\n__publicField(Twitch, \"canPlay\", import_patterns.canPlay.twitch);\n__publicField(Twitch, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Twitch_exports", "target", "all", "name", "__export", "default", "Twitch", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "props", "config", "playerId", "randomString", "componentDidMount", "onMount", "load", "url", "isReady", "playsinline", "onError", "controls", "isChannel", "MATCH_URL_TWITCH_CHANNEL", "test", "id", "match", "MATCH_URL_TWITCH_VIDEO", "player", "setChannel", "setVideo", "getSDK", "then", "Twitch2", "Player", "playerID", "video", "channel", "height", "width", "autoplay", "playing", "muted", "time", "parseStartTime", "options", "READY", "PLAYING", "PAUSE", "ENDED", "ONLINE", "OFFLINE", "SEEK", "addEventListener", "onReady", "onPlay", "onPause", "onEnded", "onSeek", "onLoaded", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "createElement", "style", "canPlay", "twitch"], "sourceRoot": ""}
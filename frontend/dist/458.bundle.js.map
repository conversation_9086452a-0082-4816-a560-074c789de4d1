{"version": 3, "file": "458.bundle.js", "mappings": "2HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBX,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAoB,CAC3BK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAC9B,MAAMC,EAAqC,oBAAdC,UACvBC,EAAcF,GAAwC,aAAvBC,UAAUE,UAA2BF,UAAUG,eAAiB,EAC/FC,EAASL,IAAkB,mBAAmBM,KAAKL,UAAUM,YAAcL,KAAiBM,OAAOC,SACnGC,EAAYV,GAAiB,iCAAiCM,KAAKL,UAAUM,aAAeC,OAAOC,SAOnGE,EAAoB,wBACpBC,EAA0B,sDAEhC,MAAMrB,UAAmBG,EAAamB,UACpC,WAAAC,GACEC,SAASC,WAETrC,EAAcsC,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMC,WAAWF,IAClEvC,EAAcsC,KAAM,SAAU,IAAIC,IAASD,KAAKE,MAAME,UAAUH,IAChEvC,EAAcsC,KAAM,WAAY,IAAIC,IAASD,KAAKE,MAAMG,YAAYJ,IACpEvC,EAAcsC,KAAM,cAAe,IAAIC,IAASD,KAAKE,MAAMI,eAAeL,IAC1EvC,EAAcsC,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMK,WAAWN,IAClEvC,EAAcsC,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMM,WAAWP,IAClEvC,EAAcsC,KAAM,UAAW,IAAIC,IAASD,KAAKE,MAAMO,WAAWR,IAClEvC,EAAcsC,KAAM,uBAAyBU,GAAUV,KAAKE,MAAMS,qBAAqBD,EAAMzC,OAAO2C,eACpGlD,EAAcsC,KAAM,cAAe,IAAIC,IAASD,KAAKE,MAAMW,eAAeZ,IAC1EvC,EAAcsC,KAAM,eAAiBc,IACnC,MAAM,aAAEC,EAAY,QAAEC,GAAYhB,KAAKE,MACvCa,EAAaD,GACTE,GACFhB,KAAKiB,SAGTvD,EAAcsC,KAAM,2BAA6Bc,IAC/C,GAAId,KAAKkB,SAAU,EAAIrC,EAAasC,gCAAgCnB,KAAKkB,QAAS,CAChF,MAAM,uBAAEE,GAA2BpB,KAAKkB,OACT,uBAA3BE,EACFpB,KAAKa,YAAYC,GACmB,WAA3BM,GACTpB,KAAKe,aAAaD,EAEtB,IAEFpD,EAAcsC,KAAM,SAAWc,IAC7Bd,KAAKE,MAAMmB,OAAOP,EAAE7C,OAAOqD,eAE7B5D,EAAcsC,KAAM,OAAQ,KAC1BA,KAAKkB,OAAOK,OAAQ,IAEtB7D,EAAcsC,KAAM,SAAU,KAC5BA,KAAKkB,OAAOK,OAAQ,IAEtB7D,EAAcsC,KAAM,sBAAuB,CAACwB,EAAQC,IAC5B,iBAAXD,EACc/C,EAAaJ,QAAQqD,cAAc,SAAU,CAAEpE,IAAKmE,EAAOE,IAAKH,IAElE/C,EAAaJ,QAAQqD,cAAc,SAAU,CAAEpE,IAAKmE,KAAUD,KAEvF9D,EAAcsC,KAAM,cAAe,CAAC4B,EAAOH,IAClBhD,EAAaJ,QAAQqD,cAAc,QAAS,CAAEpE,IAAKmE,KAAUG,KAEtFlE,EAAcsC,KAAM,MAAQkB,IACtBlB,KAAKkB,SACPlB,KAAK6B,WAAa7B,KAAKkB,QAEzBlB,KAAKkB,OAASA,GAElB,CACA,iBAAAY,GACE9B,KAAKE,MAAM6B,SAAW/B,KAAKE,MAAM6B,QAAQ/B,MACzCA,KAAKgC,aAAahC,KAAKkB,QACvB,MAAMS,EAAM3B,KAAKiC,UAAUjC,KAAKE,MAAMgC,KAClCP,IACF3B,KAAKkB,OAAOS,IAAMA,IAEhBvC,GAAUY,KAAKE,MAAMiC,OAAOC,kBAC9BpC,KAAKkB,OAAOmB,MAEhB,CACA,kBAAAC,CAAmBC,GACbvC,KAAKwC,eAAexC,KAAKE,SAAWF,KAAKwC,eAAeD,KAC1DvC,KAAKyC,gBAAgBzC,KAAK6B,WAAYU,EAAUL,KAChDlC,KAAKgC,aAAahC,KAAKkB,SAErBlB,KAAKE,MAAMgC,MAAQK,EAAUL,MAAQ,EAAIrD,EAAa6D,eAAe1C,KAAKE,MAAMgC,MAAUlC,KAAKE,MAAMgC,eAAeS,QACtH3C,KAAKkB,OAAO0B,UAAY,KAE5B,CACA,oBAAAC,GACE7C,KAAKkB,OAAO4B,gBAAgB,OAC5B9C,KAAKyC,gBAAgBzC,KAAKkB,QACtBlB,KAAK+C,KACP/C,KAAK+C,IAAIC,SAEb,CACA,YAAAhB,CAAad,GACX,MAAM,IAAEgB,EAAG,YAAEe,GAAgBjD,KAAKE,MAClCgB,EAAOgC,iBAAiB,OAAQlD,KAAKI,QACrCc,EAAOgC,iBAAiB,UAAWlD,KAAKK,UACxCa,EAAOgC,iBAAiB,UAAWlD,KAAKM,aACxCY,EAAOgC,iBAAiB,QAASlD,KAAKO,SACtCW,EAAOgC,iBAAiB,SAAUlD,KAAKqB,QACvCH,EAAOgC,iBAAiB,QAASlD,KAAKQ,SACtCU,EAAOgC,iBAAiB,QAASlD,KAAKS,SACtCS,EAAOgC,iBAAiB,aAAclD,KAAKmD,sBAC3CjC,EAAOgC,iBAAiB,wBAAyBlD,KAAKa,aACtDK,EAAOgC,iBAAiB,wBAAyBlD,KAAKe,cACtDG,EAAOgC,iBAAiB,gCAAiClD,KAAKoD,0BACzDpD,KAAKqD,aAAanB,IACrBhB,EAAOgC,iBAAiB,UAAWlD,KAAKG,SAEtC8C,IACF/B,EAAOoC,aAAa,cAAe,IACnCpC,EAAOoC,aAAa,qBAAsB,IAC1CpC,EAAOoC,aAAa,iBAAkB,IAE1C,CACA,eAAAb,CAAgBvB,EAAQgB,GACtBhB,EAAOqC,oBAAoB,UAAWvD,KAAKG,SAC3Ce,EAAOqC,oBAAoB,OAAQvD,KAAKI,QACxCc,EAAOqC,oBAAoB,UAAWvD,KAAKK,UAC3Ca,EAAOqC,oBAAoB,UAAWvD,KAAKM,aAC3CY,EAAOqC,oBAAoB,QAASvD,KAAKO,SACzCW,EAAOqC,oBAAoB,SAAUvD,KAAKqB,QAC1CH,EAAOqC,oBAAoB,QAASvD,KAAKQ,SACzCU,EAAOqC,oBAAoB,QAASvD,KAAKS,SACzCS,EAAOqC,oBAAoB,aAAcvD,KAAKmD,sBAC9CjC,EAAOqC,oBAAoB,wBAAyBvD,KAAKa,aACzDK,EAAOqC,oBAAoB,wBAAyBvD,KAAKe,cACzDG,EAAOqC,oBAAoB,gCAAiCvD,KAAKoD,0BAC5DpD,KAAKqD,aAAanB,IACrBhB,EAAOqC,oBAAoB,UAAWvD,KAAKG,QAE/C,CACA,cAAAqC,CAAetC,GACb,OAAIA,EAAMiC,OAAOqB,aAGbtD,EAAMiC,OAAOsB,WAAWC,SAGrB5E,EAAgB6E,iBAAiBtE,KAAKa,EAAMgC,MAAQhC,EAAMiC,OAAOyB,WAC1E,CACA,YAAAP,CAAanB,GACX,SAAIzC,GAAaO,KAAKE,MAAMiC,OAAO0B,gBAAkB7D,KAAKE,MAAMiC,OAAO2B,YAGnE1E,IAAUY,KAAKE,MAAMiC,OAAOC,kBAGzBtD,EAAgBiF,eAAe1E,KAAK6C,IAAQvC,EAAwBN,KAAK6C,GAClF,CACA,aAAA8B,CAAc9B,GACZ,OAAOpD,EAAgBmF,gBAAgB5E,KAAK6C,IAAQlC,KAAKE,MAAMiC,OAAO+B,SACxE,CACA,YAAAC,CAAajC,GACX,OAAOpD,EAAgBsF,eAAe/E,KAAK6C,IAAQlC,KAAKE,MAAMiC,OAAOkC,QACvE,CACA,IAAAhC,CAAKH,GACH,MAAM,WAAEoC,EAAU,WAAEC,EAAU,YAAEC,EAAW,WAAEC,GAAezE,KAAKE,MAAMiC,OAkDvE,GAjDInC,KAAK+C,KACP/C,KAAK+C,IAAIC,UAEPhD,KAAK0E,MACP1E,KAAK0E,KAAKC,QAER3E,KAAKqD,aAAanB,KACpB,EAAIrD,EAAa+F,QAnKH,8DAmKuBC,QAAQ,UAAWP,GAlK3C,OAkKoEQ,KAAMC,IAQrF,GAPA/E,KAAK+C,IAAM,IAAIgC,EAAIR,GACnBvE,KAAK+C,IAAIiC,GAAGD,EAAIE,OAAOC,gBAAiB,KACtClF,KAAKE,MAAMC,YAEbH,KAAK+C,IAAIiC,GAAGD,EAAIE,OAAOE,MAAO,CAACrE,EAAGsE,KAChCpF,KAAKE,MAAMO,QAAQK,EAAGsE,EAAMpF,KAAK+C,IAAKgC,KAEpCpF,EAAwBN,KAAK6C,GAAM,CACrC,MAAMmD,EAAKnD,EAAIoD,MAAM3F,GAAyB,GAC9CK,KAAK+C,IAAIwC,WArKe,qDAqKsBV,QAAQ,OAAQQ,GAChE,MACErF,KAAK+C,IAAIwC,WAAWrD,GAEtBlC,KAAK+C,IAAIyC,YAAYxF,KAAKkB,QAC1BlB,KAAKE,MAAMuF,aAGXzF,KAAKgE,cAAc9B,KACrB,EAAIrD,EAAa+F,QApLF,wEAoLuBC,QAAQ,UAAWL,GAnL3C,UAmLsEM,KAAMY,IACxF1F,KAAK0E,KAAOgB,EAAOC,cAActJ,SACjC2D,KAAK0E,KAAKkB,WAAW5F,KAAKkB,OAAQgB,EAAKlC,KAAKE,MAAMc,SAClDhB,KAAK0E,KAAKM,GAAG,QAAShF,KAAKE,MAAMO,SAC7BoF,SAASrB,GAAe,EAC1BxE,KAAK0E,KAAKoB,WAAWC,wBAAuB,GAE5C/F,KAAK0E,KAAKsB,eAAe,CAAEC,MAAO,CAAEC,SAAUR,EAAOS,MAAMC,kBAE7DpG,KAAKE,MAAMuF,aAGXzF,KAAKmE,aAAajC,KACpB,EAAIrD,EAAa+F,QA/LH,8DA+LuBC,QAAQ,UAAWJ,GA9L3C,SA8LoEK,KAAMuB,IACrFrG,KAAKsG,IAAMD,EAAME,aAAa,CAAEC,KAAM,MAAOtE,QAC7ClC,KAAKsG,IAAIG,mBAAmBzG,KAAKkB,QACjClB,KAAKsG,IAAItB,GAAGqB,EAAMpB,OAAOE,MAAO,CAACrE,EAAGsE,KAClCpF,KAAKE,MAAMO,QAAQK,EAAGsE,EAAMpF,KAAKsG,IAAKD,KAExCrG,KAAKsG,IAAIjE,OACTrC,KAAKE,MAAMuF,aAGXvD,aAAeS,MACjB3C,KAAKkB,OAAOmB,YACP,IAAI,EAAIxD,EAAa6D,eAAeR,GACzC,IACElC,KAAKkB,OAAO0B,UAAYV,CAC1B,CAAE,MAAOpB,GACPd,KAAKkB,OAAOS,IAAMpC,OAAOmH,IAAIC,gBAAgBzE,EAC/C,CAEJ,CACA,IAAAjB,GACE,MAAM2F,EAAU5G,KAAKkB,OAAOD,OACxB2F,GACFA,EAAQC,MAAM7G,KAAKE,MAAMO,QAE7B,CACA,KAAAqG,GACE9G,KAAKkB,OAAO4F,OACd,CACA,IAAAC,GACE/G,KAAKkB,OAAO4B,gBAAgB,OACxB9C,KAAK0E,MACP1E,KAAK0E,KAAKC,OAEd,CACA,MAAAqC,CAAOC,EAASC,GAAc,GAC5BlH,KAAKkB,OAAOI,YAAc2F,EACrBC,GACHlH,KAAK8G,OAET,CACA,SAAAK,CAAUC,GACRpH,KAAKkB,OAAOmG,OAASD,CACvB,CACA,SAAAE,GACMtH,KAAKkB,OAAOqG,yBAA2BC,SAASC,0BAA4BzH,KAAKkB,OACnFlB,KAAKkB,OAAOqG,2BACH,EAAI1I,EAAasC,gCAAgCnB,KAAKkB,SAAkD,uBAAvClB,KAAKkB,OAAOE,wBACtFpB,KAAKkB,OAAOwG,0BAA0B,qBAE1C,CACA,UAAAC,GACMH,SAASI,sBAAwBJ,SAASC,0BAA4BzH,KAAKkB,OAC7EsG,SAASI,wBACA,EAAI/I,EAAasC,gCAAgCnB,KAAKkB,SAAkD,WAAvClB,KAAKkB,OAAOE,wBACtFpB,KAAKkB,OAAOwG,0BAA0B,SAE1C,CACA,eAAAG,CAAgBC,GACd,IACE9H,KAAKkB,OAAON,aAAekH,CAC7B,CAAE,MAAOC,GACP/H,KAAKE,MAAMO,QAAQsH,EACrB,CACF,CACA,WAAAC,GACE,IAAKhI,KAAKkB,OACR,OAAO,KACT,MAAM,SAAE+G,EAAQ,SAAEC,GAAalI,KAAKkB,OACpC,OAAI+G,IAAaE,KAAYD,EAASE,OAAS,EACtCF,EAASG,IAAIH,EAASE,OAAS,GAEjCH,CACT,CACA,cAAAK,GACE,OAAKtI,KAAKkB,OAEHlB,KAAKkB,OAAOI,YADV,IAEX,CACA,gBAAAiH,GACE,IAAKvI,KAAKkB,OACR,OAAO,KACT,MAAM,SAAEsH,GAAaxI,KAAKkB,OAC1B,GAAwB,IAApBsH,EAASJ,OACX,OAAO,EAET,MAAMC,EAAMG,EAASH,IAAIG,EAASJ,OAAS,GACrCH,EAAWjI,KAAKgI,cACtB,OAAIK,EAAMJ,EACDA,EAEFI,CACT,CACA,SAAApG,CAAUC,GACR,MAAMuG,EAASzI,KAAKqD,aAAanB,GAC3BwG,EAAU1I,KAAKgE,cAAc9B,GAC7ByG,EAAS3I,KAAKmE,aAAajC,GACjC,KAAIA,aAAeS,QAAS,EAAI9D,EAAa6D,eAAeR,IAAQuG,GAAUC,GAAWC,GAGzF,OAAIjJ,EAAkBL,KAAK6C,GAClBA,EAAI2C,QAAQ,kBAAmB,6BAEjC3C,CACT,CACA,MAAA0G,GACE,MAAM,IAAE1G,EAAG,QAAElB,EAAO,KAAE6H,EAAI,SAAEC,EAAQ,MAAEvH,EAAK,OAAEY,EAAM,MAAE4G,EAAK,OAAEC,GAAWhJ,KAAKE,MAEtE+I,EADWjJ,KAAKwC,eAAexC,KAAKE,OACf,QAAU,QAC/BgJ,EAAQ,CACZH,MAAiB,SAAVA,EAAmBA,EAAQ,OAClCC,OAAmB,SAAXA,EAAoBA,EAAS,QAEvC,OAAuBvK,EAAaJ,QAAQqD,cAC1CuH,EACA,CACEE,IAAKnJ,KAAKmJ,IACVxH,IAAK3B,KAAKiC,UAAUC,GACpBgH,QACAE,QAAS,OACTC,SAAUrI,QAAW,EACrB8H,WACAvH,QACAsH,UACG1G,EAAOsB,YAEZvB,aAAeS,OAAST,EAAIoH,IAAItJ,KAAKuJ,qBACrCpH,EAAOqH,OAAOF,IAAItJ,KAAKyJ,aAE3B,EAEF/L,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWQ,EAAgB4K,QAAQC,K", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/FilePlayer.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar FilePlayer_exports = {};\n__export(FilePlayer_exports, {\n  default: () => FilePlayer\n});\nmodule.exports = __toCommonJS(FilePlayer_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst HAS_NAVIGATOR = typeof navigator !== \"undefined\";\nconst IS_IPAD_PRO = HAS_NAVIGATOR && navigator.platform === \"MacIntel\" && navigator.maxTouchPoints > 1;\nconst IS_IOS = HAS_NAVIGATOR && (/iPad|iPhone|iPod/.test(navigator.userAgent) || IS_IPAD_PRO) && !window.MSStream;\nconst IS_SAFARI = HAS_NAVIGATOR && /^((?!chrome|android).)*safari/i.test(navigator.userAgent) && !window.MSStream;\nconst HLS_SDK_URL = \"https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js\";\nconst HLS_GLOBAL = \"Hls\";\nconst DASH_SDK_URL = \"https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js\";\nconst DASH_GLOBAL = \"dashjs\";\nconst FLV_SDK_URL = \"https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js\";\nconst FLV_GLOBAL = \"flvjs\";\nconst MATCH_DROPBOX_URL = /www\\.dropbox\\.com\\/.+/;\nconst MATCH_CLOUDFLARE_STREAM = /https:\\/\\/watch\\.cloudflarestream\\.com\\/([a-z0-9]+)/;\nconst REPLACE_CLOUDFLARE_STREAM = \"https://videodelivery.net/{id}/manifest/video.m3u8\";\nclass FilePlayer extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onDisablePIP\", (e) => {\n      const { onDisablePIP, playing } = this.props;\n      onDisablePIP(e);\n      if (playing) {\n        this.play();\n      }\n    });\n    __publicField(this, \"onPresentationModeChange\", (e) => {\n      if (this.player && (0, import_utils.supportsWebKitPresentationMode)(this.player)) {\n        const { webkitPresentationMode } = this.player;\n        if (webkitPresentationMode === \"picture-in-picture\") {\n          this.onEnablePIP(e);\n        } else if (webkitPresentationMode === \"inline\") {\n          this.onDisablePIP(e);\n        }\n      }\n    });\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"renderSourceElement\", (source, index) => {\n      if (typeof source === \"string\") {\n        return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, src: source });\n      }\n      return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, ...source });\n    });\n    __publicField(this, \"renderTrack\", (track, index) => {\n      return /* @__PURE__ */ import_react.default.createElement(\"track\", { key: index, ...track });\n    });\n    __publicField(this, \"ref\", (player) => {\n      if (this.player) {\n        this.prevPlayer = this.player;\n      }\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const src = this.getSource(this.props.url);\n    if (src) {\n      this.player.src = src;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      this.player.load();\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.shouldUseAudio(this.props) !== this.shouldUseAudio(prevProps)) {\n      this.removeListeners(this.prevPlayer, prevProps.url);\n      this.addListeners(this.player);\n    }\n    if (this.props.url !== prevProps.url && !(0, import_utils.isMediaStream)(this.props.url) && !(this.props.url instanceof Array)) {\n      this.player.srcObject = null;\n    }\n  }\n  componentWillUnmount() {\n    this.player.removeAttribute(\"src\");\n    this.removeListeners(this.player);\n    if (this.hls) {\n      this.hls.destroy();\n    }\n  }\n  addListeners(player) {\n    const { url, playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.addEventListener(\"canplay\", this.onReady);\n    }\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n      player.setAttribute(\"webkit-playsinline\", \"\");\n      player.setAttribute(\"x5-playsinline\", \"\");\n    }\n  }\n  removeListeners(player, url) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.removeEventListener(\"canplay\", this.onReady);\n    }\n  }\n  shouldUseAudio(props) {\n    if (props.config.forceVideo) {\n      return false;\n    }\n    if (props.config.attributes.poster) {\n      return false;\n    }\n    return import_patterns.AUDIO_EXTENSIONS.test(props.url) || props.config.forceAudio;\n  }\n  shouldUseHLS(url) {\n    if (IS_SAFARI && this.props.config.forceSafariHLS || this.props.config.forceHLS) {\n      return true;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      return false;\n    }\n    return import_patterns.HLS_EXTENSIONS.test(url) || MATCH_CLOUDFLARE_STREAM.test(url);\n  }\n  shouldUseDASH(url) {\n    return import_patterns.DASH_EXTENSIONS.test(url) || this.props.config.forceDASH;\n  }\n  shouldUseFLV(url) {\n    return import_patterns.FLV_EXTENSIONS.test(url) || this.props.config.forceFLV;\n  }\n  load(url) {\n    const { hlsVersion, hlsOptions, dashVersion, flvVersion } = this.props.config;\n    if (this.hls) {\n      this.hls.destroy();\n    }\n    if (this.dash) {\n      this.dash.reset();\n    }\n    if (this.shouldUseHLS(url)) {\n      (0, import_utils.getSDK)(HLS_SDK_URL.replace(\"VERSION\", hlsVersion), HLS_GLOBAL).then((Hls) => {\n        this.hls = new Hls(hlsOptions);\n        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {\n          this.props.onReady();\n        });\n        this.hls.on(Hls.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.hls, Hls);\n        });\n        if (MATCH_CLOUDFLARE_STREAM.test(url)) {\n          const id = url.match(MATCH_CLOUDFLARE_STREAM)[1];\n          this.hls.loadSource(REPLACE_CLOUDFLARE_STREAM.replace(\"{id}\", id));\n        } else {\n          this.hls.loadSource(url);\n        }\n        this.hls.attachMedia(this.player);\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseDASH(url)) {\n      (0, import_utils.getSDK)(DASH_SDK_URL.replace(\"VERSION\", dashVersion), DASH_GLOBAL).then((dashjs) => {\n        this.dash = dashjs.MediaPlayer().create();\n        this.dash.initialize(this.player, url, this.props.playing);\n        this.dash.on(\"error\", this.props.onError);\n        if (parseInt(dashVersion) < 3) {\n          this.dash.getDebug().setLogToBrowserConsole(false);\n        } else {\n          this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } });\n        }\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseFLV(url)) {\n      (0, import_utils.getSDK)(FLV_SDK_URL.replace(\"VERSION\", flvVersion), FLV_GLOBAL).then((flvjs) => {\n        this.flv = flvjs.createPlayer({ type: \"flv\", url });\n        this.flv.attachMediaElement(this.player);\n        this.flv.on(flvjs.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.flv, flvjs);\n        });\n        this.flv.load();\n        this.props.onLoaded();\n      });\n    }\n    if (url instanceof Array) {\n      this.player.load();\n    } else if ((0, import_utils.isMediaStream)(url)) {\n      try {\n        this.player.srcObject = url;\n      } catch (e) {\n        this.player.src = window.URL.createObjectURL(url);\n      }\n    }\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.removeAttribute(\"src\");\n    if (this.dash) {\n      this.dash.reset();\n    }\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"picture-in-picture\") {\n      this.player.webkitSetPresentationMode(\"picture-in-picture\");\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"inline\") {\n      this.player.webkitSetPresentationMode(\"inline\");\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getSource(url) {\n    const useHLS = this.shouldUseHLS(url);\n    const useDASH = this.shouldUseDASH(url);\n    const useFLV = this.shouldUseFLV(url);\n    if (url instanceof Array || (0, import_utils.isMediaStream)(url) || useHLS || useDASH || useFLV) {\n      return void 0;\n    }\n    if (MATCH_DROPBOX_URL.test(url)) {\n      return url.replace(\"www.dropbox.com\", \"dl.dropboxusercontent.com\");\n    }\n    return url;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const useAudio = this.shouldUseAudio(this.props);\n    const Element = useAudio ? \"audio\" : \"video\";\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      Element,\n      {\n        ref: this.ref,\n        src: this.getSource(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        controls,\n        muted,\n        loop,\n        ...config.attributes\n      },\n      url instanceof Array && url.map(this.renderSourceElement),\n      config.tracks.map(this.renderTrack)\n    );\n  }\n}\n__publicField(FilePlayer, \"displayName\", \"FilePlayer\");\n__publicField(FilePlayer, \"canPlay\", import_patterns.canPlay.file);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "FilePlayer_exports", "target", "all", "name", "__export", "default", "FilePlayer", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "HAS_NAVIGATOR", "navigator", "IS_IPAD_PRO", "platform", "maxTouchPoints", "IS_IOS", "test", "userAgent", "window", "MSStream", "IS_SAFARI", "MATCH_DROPBOX_URL", "MATCH_CLOUDFLARE_STREAM", "Component", "constructor", "super", "arguments", "this", "args", "props", "onReady", "onPlay", "onBuffer", "onBufferEnd", "onPause", "onEnded", "onError", "event", "onPlaybackRateChange", "playbackRate", "onEnablePIP", "e", "onDisablePIP", "playing", "play", "player", "supportsWebKitPresentationMode", "webkitPresentationMode", "onSeek", "currentTime", "muted", "source", "index", "createElement", "src", "track", "prevPlayer", "componentDidMount", "onMount", "addListeners", "getSource", "url", "config", "forceDisableHls", "load", "componentDidUpdate", "prevProps", "shouldUseAudio", "removeListeners", "isMediaStream", "Array", "srcObject", "componentWillUnmount", "removeAttribute", "hls", "destroy", "playsinline", "addEventListener", "onPlayBackRateChange", "onPresentationModeChange", "shouldUseHLS", "setAttribute", "removeEventListener", "forceVideo", "attributes", "poster", "AUDIO_EXTENSIONS", "forceAudio", "forceSafariHLS", "forceHLS", "HLS_EXTENSIONS", "shouldUseDASH", "DASH_EXTENSIONS", "forceDASH", "shouldUseFLV", "FLV_EXTENSIONS", "forceFLV", "hlsVersion", "hlsOptions", "dashVersion", "flvVersion", "dash", "reset", "getSDK", "replace", "then", "Hls", "on", "Events", "MANIFEST_PARSED", "ERROR", "data", "id", "match", "loadSource", "attachMedia", "onLoaded", "dashjs", "MediaPlayer", "initialize", "parseInt", "getDebug", "setLogToBrowserConsole", "updateSettings", "debug", "logLevel", "Debug", "LOG_LEVEL_NONE", "flvjs", "flv", "createPlayer", "type", "attachMediaElement", "URL", "createObjectURL", "promise", "catch", "pause", "stop", "seekTo", "seconds", "keepPlaying", "setVolume", "fraction", "volume", "enablePIP", "requestPictureInPicture", "document", "pictureInPictureElement", "webkitSetPresentationMode", "disablePIP", "exitPictureInPicture", "setPlaybackRate", "rate", "error", "getDuration", "duration", "seekable", "Infinity", "length", "end", "getCurrentTime", "getSecondsLoaded", "buffered", "useHLS", "useDASH", "useFLV", "render", "loop", "controls", "width", "height", "Element", "style", "ref", "preload", "autoPlay", "map", "renderSourceElement", "tracks", "renderTrack", "canPlay", "file"], "sourceRoot": ""}
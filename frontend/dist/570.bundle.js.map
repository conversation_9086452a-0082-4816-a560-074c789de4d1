{"version": 3, "file": "570.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAmB,CAAC,EAzBT,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAkB,CACzBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAG9B,MAAMR,UAAiBG,EAAaM,UAClC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAY,MAChCzB,EAAcyB,KAAM,cAAe,MACnCzB,EAAcyB,KAAM,gBAAiB,MACrCzB,EAAcyB,KAAM,OAAQ,QAE5BzB,EAAcyB,KAAM,SAAU,QAE9BzB,EAAcyB,KAAM,MAAQE,IAC1BF,KAAKE,OAASA,GAElB,CACA,iBAAAC,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACA,IAAAM,CAAKC,IACH,EAAIb,EAAac,QArBL,oDACG,YAoB+BC,KAAMC,IAClDV,KAAKW,OAASD,EAAUE,aAAaZ,KAAKE,QAC1CF,KAAKW,OAAOE,MAAMJ,KAAK,KACrBT,KAAKW,OAAOG,OAAOC,KAAKC,GAAGhB,KAAKI,MAAMa,QACtCjB,KAAKW,OAAOG,OAAOI,MAAMF,GAAGhB,KAAKI,MAAMe,SACvCnB,KAAKW,OAAOG,OAAOM,MAAMJ,GAAGhB,KAAKI,MAAMiB,SACvCrB,KAAKW,OAAOG,OAAOQ,MAAMN,GAAGhB,KAAKI,MAAMkB,OACvCtB,KAAKW,OAAOG,OAAOS,SAASP,GAAG,CAACQ,EAASC,KACvCzB,KAAK0B,YAAcF,EACnBxB,KAAKyB,SAAWA,IAElBzB,KAAKI,MAAMuB,aAEZ3B,KAAKI,MAAMwB,QAChB,CACA,IAAAb,GACEf,KAAKC,WAAW,OAClB,CACA,KAAAiB,GACElB,KAAKC,WAAW,QAClB,CACA,IAAA4B,GACA,CACA,MAAAC,CAAON,EAASO,GAAc,GAC5B/B,KAAKC,WAAW,OAAQuB,GACnBO,GACH/B,KAAKkB,OAET,CACA,SAAAc,CAAUC,GACV,CACA,WAAAC,GACE,OAAOlC,KAAKyB,QACd,CACA,cAAAU,GACE,OAAOnC,KAAK0B,WACd,CACA,gBAAAU,GACE,OAAO,IACT,CACA,MAAAC,GACE,MAAM,IAAE9B,EAAG,OAAE+B,GAAWtC,KAAKI,MACvBmC,EAAKhC,EAAIiC,MAAM7C,EAAgB8C,oBAAoB,GAKnDC,GAAQ,EAAIhD,EAAaiD,aAAa,IACvCL,EAAOM,QACVC,KAAM,IAAIN,OAEZ,OAAuBjD,EAAaJ,QAAQ4D,cAC1C,SACA,CACE3E,IAAKoE,EACLQ,IAAK/C,KAAK+C,IACVC,MAbU,CACZC,MAAO,OACPC,OAAQ,QAYNC,IAAK,qDAAqDT,IAC1DU,YAAa,IACbC,MAAO,YAGb,EAEF9E,EAAcY,EAAU,cAAe,YACvCZ,EAAcY,EAAU,UAAWQ,EAAgB2D,QAAQC,UAC3DhF,EAAcY,EAAU,eAAe,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Mixcloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mixcloud_exports = {};\n__export(Mixcloud_exports, {\n  default: () => Mixcloud\n});\nmodule.exports = __toCommonJS(Mixcloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://widget.mixcloud.com/media/js/widgetApi.js\";\nconst SDK_GLOBAL = \"Mixcloud\";\nclass Mixcloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n    });\n    __publicField(this, \"unmute\", () => {\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Mixcloud2) => {\n      this.player = Mixcloud2.PlayerWidget(this.iframe);\n      this.player.ready.then(() => {\n        this.player.events.play.on(this.props.onPlay);\n        this.player.events.pause.on(this.props.onPause);\n        this.player.events.ended.on(this.props.onEnded);\n        this.player.events.error.on(this.props.error);\n        this.player.events.progress.on((seconds, duration) => {\n          this.currentTime = seconds;\n          this.duration = duration;\n        });\n        this.props.onReady();\n      });\n    }, this.props.onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url, config } = this.props;\n    const id = url.match(import_patterns.MATCH_URL_MIXCLOUD)[1];\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    const query = (0, import_utils.queryString)({\n      ...config.options,\n      feed: `/${id}/`\n    });\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        key: id,\n        ref: this.ref,\n        style,\n        src: `https://player-widget.mixcloud.com/widget/iframe/?${query}`,\n        frameBorder: \"0\",\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(Mixcloud, \"displayName\", \"Mixcloud\");\n__publicField(Mixcloud, \"canPlay\", import_patterns.canPlay.mixcloud);\n__publicField(Mixcloud, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Mixcloud_exports", "target", "all", "name", "__export", "default", "Mixcloud", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "Mixcloud2", "player", "PlayerWidget", "ready", "events", "play", "on", "onPlay", "pause", "onPause", "ended", "onEnded", "error", "progress", "seconds", "duration", "currentTime", "onReady", "onError", "stop", "seekTo", "keepPlaying", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "config", "id", "match", "MATCH_URL_MIXCLOUD", "query", "queryString", "options", "feed", "createElement", "ref", "style", "width", "height", "src", "frameBorder", "allow", "canPlay", "mixcloud"], "sourceRoot": ""}
(global.webpackChunkhelios_subtitle_corrector=global.webpackChunkhelios_subtitle_corrector||[]).push([[353],{6734:(e,t,r)=>{var a,l=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,p=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of n(t))c.call(e,l)||l===r||i(e,l,{get:()=>t[l],enumerable:!(a=o(t,l))||a.enumerable});return e},u=(e,t,r)=>(((e,t,r)=>{t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r),h={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(h,{default:()=>g}),e.exports=(a=h,p(i({},"__esModule",{value:!0}),a));var d=((e,t,r)=>(r=null!=e?l(s(e)):{},p(e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(6540));const b="64px",m={};class g extends d.Component{constructor(){super(...arguments),u(this,"mounted",!1),u(this,"state",{image:null}),u(this,"handleKeyPress",e=>{"Enter"!==e.key&&" "!==e.key||this.props.onClick()})}componentDidMount(){this.mounted=!0,this.fetchImage(this.props)}componentDidUpdate(e){const{url:t,light:r}=this.props;e.url===t&&e.light===r||this.fetchImage(this.props)}componentWillUnmount(){this.mounted=!1}fetchImage({url:e,light:t,oEmbedUrl:r}){if(!d.default.isValidElement(t))if("string"!=typeof t){if(!m[e])return this.setState({image:null}),window.fetch(r.replace("{url}",e)).then(e=>e.json()).then(t=>{if(t.thumbnail_url&&this.mounted){const r=t.thumbnail_url.replace("height=100","height=480").replace("-d_295x166","-d_640");this.setState({image:r}),m[e]=r}});this.setState({image:m[e]})}else this.setState({image:t})}render(){const{light:e,onClick:t,playIcon:r,previewTabIndex:a,previewAriaLabel:l}=this.props,{image:i}=this.state,o=d.default.isValidElement(e),n={display:"flex",alignItems:"center",justifyContent:"center"},s={preview:{width:"100%",height:"100%",backgroundImage:i&&!o?`url(${i})`:void 0,backgroundSize:"cover",backgroundPosition:"center",cursor:"pointer",...n},shadow:{background:"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)",borderRadius:b,width:b,height:b,position:o?"absolute":void 0,...n},playIcon:{borderStyle:"solid",borderWidth:"16px 0 16px 26px",borderColor:"transparent transparent transparent white",marginLeft:"7px"}},c=d.default.createElement("div",{style:s.shadow,className:"react-player__shadow"},d.default.createElement("div",{style:s.playIcon,className:"react-player__play-icon"}));return d.default.createElement("div",{style:s.preview,className:"react-player__preview",onClick:t,tabIndex:a,onKeyPress:this.handleKeyPress,...l?{"aria-label":l}:{}},o?e:null,r||c)}}}}]);
//# sourceMappingURL=353.bundle.js.map
{"version": 3, "file": "340.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAiB,CAAC,EAzBP,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAgB,CACvBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAI9B,MAAMR,UAAeG,EAAaM,UAChC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,WAAYA,KAAKE,MAAMC,OAAOC,UAAY,kBAAsB,EAAIV,EAAaW,mBAErG9B,EAAcyB,KAAM,SAAU,IAAIM,IAASN,KAAKE,MAAMK,UAAUD,IAChE/B,EAAcyB,KAAM,UAAW,IAAIM,IAASN,KAAKE,MAAMM,WAAWF,IAClE/B,EAAcyB,KAAM,SAAU,IAAIM,IAASN,KAAKE,MAAMO,UAAUH,IAChE/B,EAAcyB,KAAM,UAAW,IAAIM,IAASN,KAAKE,MAAMQ,WAAWJ,IAClE/B,EAAcyB,KAAM,uBAAwB,IAAIM,IAASN,KAAKE,MAAMS,wBAAwBL,IAC5F/B,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKC,WAAW,UAElB1B,EAAcyB,KAAM,SAAU,KAC5BA,KAAKC,WAAW,WAEpB,CACA,iBAAAW,GACEZ,KAAKE,MAAMW,SAAWb,KAAKE,MAAMW,QAAQb,KAC3C,CACA,IAAAc,CAAKC,GACH,MAAM,QAAEC,EAAO,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,EAAO,OAAEhB,EAAM,QAAEiB,GAAYpB,KAAKE,OACpE,EAAIR,EAAa2B,QA1BL,kDACG,UAyB+BC,KAAMC,IAC9CpB,EAAOqB,gBACTrB,EAAOqB,eAAeC,QAASC,GAAYH,EAAQI,cAAcD,IAEnEE,OAAOC,IAAMD,OAAOC,KAAO,GAC3BD,OAAOC,IAAIC,KAAK,CACdC,GAAI/B,KAAKgC,SACTC,QAAS,CACPC,SAAUlB,EACVmB,eAAgB,QAChBlB,QACAmB,sBAAuBlB,EACvBmB,iBAAkBnB,EAClBoB,QAASpB,EACTqB,oBAAqBrB,EACrBsB,eAAgBtB,EAChBuB,cAAevB,EACfwB,gBAAiBxB,EACjByB,gBAAiBzB,KACdf,EAAO8B,SAEZd,QAAUyB,IACR5C,KAAK4C,OAASA,EACd5C,KAAK6C,SACL7C,KAAK4C,OAAOE,KAAK,OAAQ9C,KAAKO,QAC9BP,KAAK4C,OAAOE,KAAK,QAAS9C,KAAKQ,SAC/BR,KAAK4C,OAAOE,KAAK,OAAQ9C,KAAKS,QAC9BT,KAAK4C,OAAOE,KAAK,MAAO9C,KAAKU,SAC7BV,KAAK4C,OAAOE,KAAK,qBAAsB9C,KAAKW,sBAC5CQ,QAGHC,EACL,CACA,MAAAyB,GACE7C,KAAK4C,OAAOC,OAAO,OAAQ7C,KAAKO,QAChCP,KAAK4C,OAAOC,OAAO,QAAS7C,KAAKQ,SACjCR,KAAK4C,OAAOC,OAAO,OAAQ7C,KAAKS,QAChCT,KAAK4C,OAAOC,OAAO,MAAO7C,KAAKU,SAC/BV,KAAK4C,OAAOC,OAAO,qBAAsB7C,KAAKW,qBAChD,CACA,IAAAoC,GACE/C,KAAKC,WAAW,OAClB,CACA,KAAA+C,GACEhD,KAAKC,WAAW,QAClB,CACA,IAAAgD,GACEjD,KAAK6C,SACL7C,KAAKC,WAAW,SAClB,CACA,MAAAiD,CAAOC,EAASC,GAAc,GAC5BpD,KAAKC,WAAW,OAAQkD,GACnBC,GACHpD,KAAKgD,OAET,CACA,SAAAK,CAAUC,GACRtD,KAAKC,WAAW,SAAUqD,EAC5B,CACA,eAAAC,CAAgBC,GACdxD,KAAKC,WAAW,eAAgBuD,EAClC,CACA,WAAAC,GACE,OAAOzD,KAAKC,WAAW,WACzB,CACA,cAAAyD,GACE,OAAO1D,KAAKC,WAAW,OACzB,CACA,gBAAA0D,GACE,OAAO,IACT,CACA,MAAAC,GACE,MAAM,IAAE7C,GAAQf,KAAKE,MACf2D,EAAU9C,GAAOA,EAAI+C,MAAMnE,EAAgBoE,kBAAkB,GAC7DC,EAAY,6BAA6BH,IAK/C,OAAuBvE,EAAaJ,QAAQ+E,cAAc,MAAO,CAAElC,GAAI/B,KAAKgC,SAAU7D,IAAK0F,EAASG,YAAWE,MAJjG,CACZC,MAAO,OACPC,OAAQ,SAGZ,EAEF7F,EAAcY,EAAQ,cAAe,UACrCZ,EAAcY,EAAQ,UAAWQ,EAAgB0E,QAAQC,QACzD/F,EAAcY,EAAQ,eAAe,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Wistia.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Wistia_exports = {};\n__export(Wistia_exports, {\n  default: () => Wistia\n});\nmodule.exports = __toCommonJS(Wistia_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://fast.wistia.com/assets/external/E-v1.js\";\nconst SDK_GLOBAL = \"Wistia\";\nconst PLAYER_ID_PREFIX = \"wistia-player-\";\nclass Wistia extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onSeek\", (...args) => this.props.onSeek(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onPlaybackRateChange\", (...args) => this.props.onPlaybackRateChange(...args));\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, muted, controls, onReady, config, onError } = this.props;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Wistia2) => {\n      if (config.customControls) {\n        config.customControls.forEach((control) => Wistia2.defineControl(control));\n      }\n      window._wq = window._wq || [];\n      window._wq.push({\n        id: this.playerID,\n        options: {\n          autoPlay: playing,\n          silentAutoPlay: \"allow\",\n          muted,\n          controlsVisibleOnLoad: controls,\n          fullscreenButton: controls,\n          playbar: controls,\n          playbackRateControl: controls,\n          qualityControl: controls,\n          volumeControl: controls,\n          settingsControl: controls,\n          smallPlayButton: controls,\n          ...config.options\n        },\n        onReady: (player) => {\n          this.player = player;\n          this.unbind();\n          this.player.bind(\"play\", this.onPlay);\n          this.player.bind(\"pause\", this.onPause);\n          this.player.bind(\"seek\", this.onSeek);\n          this.player.bind(\"end\", this.onEnded);\n          this.player.bind(\"playbackratechange\", this.onPlaybackRateChange);\n          onReady();\n        }\n      });\n    }, onError);\n  }\n  unbind() {\n    this.player.unbind(\"play\", this.onPlay);\n    this.player.unbind(\"pause\", this.onPause);\n    this.player.unbind(\"seek\", this.onSeek);\n    this.player.unbind(\"end\", this.onEnded);\n    this.player.unbind(\"playbackratechange\", this.onPlaybackRateChange);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.unbind();\n    this.callPlayer(\"remove\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"time\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"volume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"playbackRate\", rate);\n  }\n  getDuration() {\n    return this.callPlayer(\"duration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"time\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url } = this.props;\n    const videoID = url && url.match(import_patterns.MATCH_URL_WISTIA)[1];\n    const className = `wistia_embed wistia_async_${videoID}`;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { id: this.playerID, key: videoID, className, style });\n  }\n}\n__publicField(Wistia, \"displayName\", \"Wistia\");\n__publicField(Wistia, \"canPlay\", import_patterns.canPlay.wistia);\n__publicField(Wistia, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Wistia_exports", "target", "all", "name", "__export", "default", "Wistia", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "props", "config", "playerId", "randomString", "args", "onPlay", "onPause", "onSeek", "onEnded", "onPlaybackRateChange", "componentDidMount", "onMount", "load", "url", "playing", "muted", "controls", "onReady", "onError", "getSDK", "then", "Wistia2", "customControls", "for<PERSON>ach", "control", "defineControl", "window", "_wq", "push", "id", "playerID", "options", "autoPlay", "silentAutoPlay", "controlsVisibleOnLoad", "fullscreenButton", "playbar", "playbackRateControl", "qualityControl", "volumeControl", "settingsControl", "smallPlayButton", "player", "unbind", "bind", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "setVolume", "fraction", "setPlaybackRate", "rate", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "videoID", "match", "MATCH_URL_WISTIA", "className", "createElement", "style", "width", "height", "canPlay", "wistia"], "sourceRoot": ""}
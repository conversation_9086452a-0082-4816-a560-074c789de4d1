{"version": 3, "file": "353.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBR,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAiB,CACxBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OACnC,MAAMC,EAAY,OACZC,EAAQ,CAAC,EACf,MAAMR,UAAgBG,EAAaM,UACjC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,WAAW,GAC/BzB,EAAcyB,KAAM,QAAS,CAC3BC,MAAO,OAET1B,EAAcyB,KAAM,iBAAmBE,IACvB,UAAVA,EAAE/B,KAA6B,MAAV+B,EAAE/B,KACzB6B,KAAKG,MAAMC,WAGjB,CACA,iBAAAC,GACEL,KAAKM,SAAU,EACfN,KAAKO,WAAWP,KAAKG,MACvB,CACA,kBAAAK,CAAmBC,GACjB,MAAM,IAAEC,EAAG,MAAEC,GAAUX,KAAKG,MACxBM,EAAUC,MAAQA,GAAOD,EAAUE,QAAUA,GAC/CX,KAAKO,WAAWP,KAAKG,MAEzB,CACA,oBAAAS,GACEZ,KAAKM,SAAU,CACjB,CACA,UAAAC,EAAW,IAAEG,EAAG,MAAEC,EAAK,UAAEE,IACvB,IAAIvB,EAAaJ,QAAQ4B,eAAeH,GAGxC,GAAqB,iBAAVA,EAAX,CAIA,IAAIhB,EAAMe,GAKV,OADAV,KAAKe,SAAS,CAAEd,MAAO,OAChBe,OAAOC,MAAMJ,EAAUK,QAAQ,QAASR,IAAMS,KAAMC,GAAaA,EAASC,QAAQF,KAAMG,IAC7F,GAAIA,EAAKC,eAAiBvB,KAAKM,QAAS,CACtC,MAAML,EAAQqB,EAAKC,cAAcL,QAAQ,aAAc,cAAcA,QAAQ,aAAc,UAC3FlB,KAAKe,SAAS,CAAEd,UAChBN,EAAMe,GAAOT,CACf,IATAD,KAAKe,SAAS,CAAEd,MAAON,EAAMe,IAF/B,MAFEV,KAAKe,SAAS,CAAEd,MAAOU,GAe3B,CACA,MAAAa,GACE,MAAM,MAAEb,EAAK,QAAEP,EAAO,SAAEqB,EAAQ,gBAAEC,EAAe,iBAAEC,GAAqB3B,KAAKG,OACvE,MAAEF,GAAUD,KAAK4B,MACjBC,EAAYvC,EAAaJ,QAAQ4B,eAAeH,GAChDmB,EAAa,CACjBC,QAAS,OACTC,WAAY,SACZC,eAAgB,UAEZC,EAAS,CACbC,QAAS,CACPC,MAAO,OACPC,OAAQ,OACRC,gBAAiBrC,IAAU4B,EAAY,OAAO5B,UAAW,EACzDsC,eAAgB,QAChBC,mBAAoB,SACpBC,OAAQ,aACLX,GAELY,OAAQ,CACNC,WAAY,2DACZC,aAAclD,EACd0C,MAAO1C,EACP2C,OAAQ3C,EACRmD,SAAUhB,EAAY,gBAAa,KAChCC,GAELL,SAAU,CACRqB,YAAa,QACbC,YAAa,mBACbC,YAAa,4CACbC,WAAY,QAGVC,EAAkC5D,EAAaJ,QAAQiE,cAAc,MAAO,CAAEC,MAAOlB,EAAOQ,OAAQW,UAAW,wBAA0C/D,EAAaJ,QAAQiE,cAAc,MAAO,CAAEC,MAAOlB,EAAOT,SAAU4B,UAAW,6BAC9O,OAAuB/D,EAAaJ,QAAQiE,cAC1C,MACA,CACEC,MAAOlB,EAAOC,QACdkB,UAAW,wBACXjD,UACAkD,SAAU5B,EACV6B,WAAYvD,KAAKwD,kBACd7B,EAAmB,CAAE,aAAcA,GAAqB,CAAC,GAE9DE,EAAYlB,EAAQ,KACpBc,GAAYyB,EAEhB,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/Preview.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Preview_exports = {};\n__export(Preview_exports, {\n  default: () => Preview\n});\nmodule.exports = __toCommonJS(Preview_exports);\nvar import_react = __toESM(require(\"react\"));\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nclass Preview extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"mounted\", false);\n    __publicField(this, \"state\", {\n      image: null\n    });\n    __publicField(this, \"handleKeyPress\", (e) => {\n      if (e.key === \"Enter\" || e.key === \" \") {\n        this.props.onClick();\n      }\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    this.fetchImage(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    const { url, light } = this.props;\n    if (prevProps.url !== url || prevProps.light !== light) {\n      this.fetchImage(this.props);\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n  }\n  fetchImage({ url, light, oEmbedUrl }) {\n    if (import_react.default.isValidElement(light)) {\n      return;\n    }\n    if (typeof light === \"string\") {\n      this.setState({ image: light });\n      return;\n    }\n    if (cache[url]) {\n      this.setState({ image: cache[url] });\n      return;\n    }\n    this.setState({ image: null });\n    return window.fetch(oEmbedUrl.replace(\"{url}\", url)).then((response) => response.json()).then((data) => {\n      if (data.thumbnail_url && this.mounted) {\n        const image = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n        this.setState({ image });\n        cache[url] = image;\n      }\n    });\n  }\n  render() {\n    const { light, onClick, playIcon, previewTabIndex, previewAriaLabel } = this.props;\n    const { image } = this.state;\n    const isElement = import_react.default.isValidElement(light);\n    const flexCenter = {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\"\n    };\n    const styles = {\n      preview: {\n        width: \"100%\",\n        height: \"100%\",\n        backgroundImage: image && !isElement ? `url(${image})` : void 0,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        cursor: \"pointer\",\n        ...flexCenter\n      },\n      shadow: {\n        background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n        borderRadius: ICON_SIZE,\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        position: isElement ? \"absolute\" : void 0,\n        ...flexCenter\n      },\n      playIcon: {\n        borderStyle: \"solid\",\n        borderWidth: \"16px 0 16px 26px\",\n        borderColor: \"transparent transparent transparent white\",\n        marginLeft: \"7px\"\n      }\n    };\n    const defaultPlayIcon = /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style: styles.preview,\n        className: \"react-player__preview\",\n        onClick,\n        tabIndex: previewTabIndex,\n        onKeyPress: this.handleKeyPress,\n        ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n      },\n      isElement ? light : null,\n      playIcon || defaultPlayIcon\n    );\n  }\n}\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Preview_exports", "target", "all", "name", "__export", "default", "Preview", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "ICON_SIZE", "cache", "Component", "constructor", "super", "arguments", "this", "image", "e", "props", "onClick", "componentDidMount", "mounted", "fetchImage", "componentDidUpdate", "prevProps", "url", "light", "componentWillUnmount", "oEmbedUrl", "isValidElement", "setState", "window", "fetch", "replace", "then", "response", "json", "data", "thumbnail_url", "render", "playIcon", "previewTabIndex", "previewAriaLabel", "state", "isElement", "flexCenter", "display", "alignItems", "justifyContent", "styles", "preview", "width", "height", "backgroundImage", "backgroundSize", "backgroundPosition", "cursor", "shadow", "background", "borderRadius", "position", "borderStyle", "borderWidth", "borderColor", "marginLeft", "defaultPlayIcon", "createElement", "style", "className", "tabIndex", "onKeyPress", "handleKeyPress"], "sourceRoot": ""}
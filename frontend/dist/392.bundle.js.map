{"version": 3, "file": "392.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBR,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAiB,CACxBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAI9B,MAAMR,UAAgBG,EAAaM,UACjC,WAAAC,GACEC,SAASC,WACTxB,EAAcyB,KAAM,aAAcN,EAAaO,YAC/C1B,EAAcyB,KAAM,OAAQ,KAC1BA,KAAKE,UAAU,KAEjB3B,EAAcyB,KAAM,SAAU,KACF,OAAtBA,KAAKG,MAAMC,QACbJ,KAAKE,UAAUF,KAAKG,MAAMC,UAG9B7B,EAAcyB,KAAM,MAAQK,IAC1BL,KAAKK,UAAYA,GAErB,CACA,iBAAAC,GACEN,KAAKG,MAAMI,SAAWP,KAAKG,MAAMI,QAAQP,KAC3C,CACA,IAAAQ,CAAKC,GACH,MAAM,QAAEC,EAAO,OAAEC,EAAM,QAAEC,EAAO,WAAEC,GAAeb,KAAKG,MAChDW,EAAKL,GAAOA,EAAIM,MAAMpB,EAAgBqB,mBAAmB,GAC3DhB,KAAKiB,QACPjB,KAAKkB,QAEP,EAAIxB,EAAayB,QA5BL,uCACG,YACM,gBA0B2CC,KAAMC,IAC/DrB,KAAKK,YAEVgB,EAASC,IAAIC,iBAAiB,CAACC,EAAMP,KAC/BjB,KAAKiB,SAGTjB,KAAKiB,OAASA,EACdjB,KAAKiB,OAAOQ,GAAG,QAASzB,KAAKG,MAAMuB,SACnC1B,KAAKiB,OAAOQ,GAAG,OAAQzB,KAAKG,MAAMwB,QAClC3B,KAAKiB,OAAOQ,GAAG,QAASzB,KAAKG,MAAMyB,SACnC5B,KAAKiB,OAAOQ,GAAG,OAAQzB,KAAKG,MAAM0B,QAClC7B,KAAKiB,OAAOQ,GAAG,iBAAkBzB,KAAKG,MAAM2B,WAC3ChB,GACHO,EAASC,IAAIS,aAAa,CACxBC,KAAMlB,EACNT,UAAWL,KAAKK,UAChB4B,SAAUvB,EAAU,EAAI,KACrBC,EAAOuB,UAEZb,EAASC,IAAIa,kBAAkBrB,GAAIM,KAAMgB,IACvCpC,KAAKqC,SAAWD,EAAKE,kBACrBzB,EAAWuB,EAAKE,uBAEjB1B,EACL,CACA,IAAA2B,GACEvC,KAAKC,WAAW,OAClB,CACA,KAAAuC,GACExC,KAAKC,WAAW,QAClB,CACA,IAAAiB,GACEuB,OAAOC,UAAUpB,IAAIqB,cAAc3C,KAAKiB,OAC1C,CACA,MAAA2B,CAAOC,EAAQC,GAAc,GAC3B9C,KAAKC,WAAW,OAAQ4C,GACnBC,GACH9C,KAAKwC,OAET,CACA,SAAAtC,CAAU6C,GACR/C,KAAKC,WAAW,YAAa8C,EAC/B,CACA,eAAAC,CAAgBC,GACdjD,KAAKC,WAAW,mBAAoBgD,EACtC,CACA,WAAAC,GACE,OAAOlD,KAAKqC,QACd,CACA,cAAAc,GACE,OAAOnD,KAAKC,WAAW,cACzB,CACA,gBAAAmD,GACE,OAAO,IACT,CACA,MAAAC,GACE,MAAM,QAAEC,GAAYtD,KAAKG,MACnBoD,EAAQ,CACZC,MAAO,OACPC,OAAQ,OACRH,WAEF,OAAuBhE,EAAaJ,QAAQwE,cAAc,MAAO,CAAEH,SAAyBjE,EAAaJ,QAAQwE,cAAc,MAAO,CAAEC,IAAK3D,KAAK2D,MACpJ,EAEFpF,EAAcY,EAAS,cAAe,WACtCZ,EAAcY,EAAS,UAAWQ,EAAgBiE,QAAQC,Q", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Vidyard.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vidyard_exports = {};\n__export(Vidyard_exports, {\n  default: () => Vidyard\n});\nmodule.exports = __toCommonJS(Vidyard_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://play.vidyard.com/embed/v4.js\";\nconst SDK_GLOBAL = \"VidyardV4\";\nconst SDK_GLOBAL_READY = \"onVidyardAPI\";\nclass Vidyard extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, config, onError, onDuration } = this.props;\n    const id = url && url.match(import_patterns.MATCH_URL_VIDYARD)[1];\n    if (this.player) {\n      this.stop();\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((Vidyard2) => {\n      if (!this.container)\n        return;\n      Vidyard2.api.addReadyListener((data, player) => {\n        if (this.player) {\n          return;\n        }\n        this.player = player;\n        this.player.on(\"ready\", this.props.onReady);\n        this.player.on(\"play\", this.props.onPlay);\n        this.player.on(\"pause\", this.props.onPause);\n        this.player.on(\"seek\", this.props.onSeek);\n        this.player.on(\"playerComplete\", this.props.onEnded);\n      }, id);\n      Vidyard2.api.renderPlayer({\n        uuid: id,\n        container: this.container,\n        autoplay: playing ? 1 : 0,\n        ...config.options\n      });\n      Vidyard2.api.getPlayerMetadata(id).then((meta) => {\n        this.duration = meta.length_in_seconds;\n        onDuration(meta.length_in_seconds);\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    window.VidyardV4.api.destroyPlayer(this.player);\n  }\n  seekTo(amount, keepPlaying = true) {\n    this.callPlayer(\"seek\", amount);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackSpeed\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"currentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(Vidyard, \"displayName\", \"Vidyard\");\n__publicField(Vidyard, \"canPlay\", import_patterns.canPlay.vidyard);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Vidyard_exports", "target", "all", "name", "__export", "default", "<PERSON><PERSON><PERSON>", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "setVolume", "props", "volume", "container", "componentDidMount", "onMount", "load", "url", "playing", "config", "onError", "onDuration", "id", "match", "MATCH_URL_VIDYARD", "player", "stop", "getSDK", "then", "Vidyard2", "api", "addReadyListener", "data", "on", "onReady", "onPlay", "onPause", "onSeek", "onEnded", "renderPlayer", "uuid", "autoplay", "options", "getPlayerMetadata", "meta", "duration", "length_in_seconds", "play", "pause", "window", "VidyardV4", "destroyPlayer", "seekTo", "amount", "keepPlaying", "fraction", "setPlaybackRate", "rate", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "display", "style", "width", "height", "createElement", "ref", "canPlay", "vidyard"], "sourceRoot": ""}
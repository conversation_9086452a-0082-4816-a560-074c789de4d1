(global.webpackChunkhelios_subtitle_corrector=global.webpackChunkhelios_subtitle_corrector||[]).push([[627],{9643:(e,t,r)=>{var s,o=Object.create,a=Object.defineProperty,l=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,u=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))p.call(e,o)||o===r||a(e,o,{get:()=>t[o],enumerable:!(s=l(t,o))||s.enumerable});return e},h=(e,t,r)=>(((e,t,r)=>{t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r),c={};((e,t)=>{for(var r in t)a(e,r,{get:t[r],enumerable:!0})})(c,{default:()=>b}),e.exports=(s=c,u(a({},"__esModule",{value:!0}),s));var d=((e,t,r)=>(r=null!=e?o(n(e)):{},u(e&&e.__esModule?r:a(r,"default",{value:e,enumerable:!0}),e)))(r(6540)),y=r(5635),m=r(327);class b extends d.Component{constructor(){super(...arguments),h(this,"callPlayer",y.callPlayer),h(this,"duration",null),h(this,"currentTime",null),h(this,"secondsLoaded",null),h(this,"mute",()=>{this.callPlayer("mute")}),h(this,"unmute",()=>{this.callPlayer("unmute")}),h(this,"ref",e=>{this.iframe=e})}componentDidMount(){this.props.onMount&&this.props.onMount(this)}load(e){(0,y.getSDK)("https://cdn.embed.ly/player-0.1.0.min.js","playerjs").then(e=>{this.iframe&&(this.player=new e.Player(this.iframe),this.player.setLoop(this.props.loop),this.player.on("ready",this.props.onReady),this.player.on("play",this.props.onPlay),this.player.on("pause",this.props.onPause),this.player.on("seeked",this.props.onSeek),this.player.on("ended",this.props.onEnded),this.player.on("error",this.props.onError),this.player.on("timeupdate",({duration:e,seconds:t})=>{this.duration=e,this.currentTime=t}),this.player.on("buffered",({percent:e})=>{this.duration&&(this.secondsLoaded=this.duration*e)}),this.props.muted&&this.player.mute())},this.props.onError)}play(){this.callPlayer("play")}pause(){this.callPlayer("pause")}stop(){}seekTo(e,t=!0){this.callPlayer("setCurrentTime",e),t||this.pause()}setVolume(e){this.callPlayer("setVolume",100*e)}setLoop(e){this.callPlayer("setLoop",e)}getDuration(){return this.duration}getCurrentTime(){return this.currentTime}getSecondsLoaded(){return this.secondsLoaded}render(){const e=this.props.url.match(m.MATCH_URL_STREAMABLE)[1];return d.default.createElement("iframe",{ref:this.ref,src:`https://streamable.com/o/${e}`,frameBorder:"0",scrolling:"no",style:{width:"100%",height:"100%"},allow:"encrypted-media; autoplay; fullscreen;"})}}h(b,"displayName","Streamable"),h(b,"canPlay",m.canPlay.streamable)}}]);
//# sourceMappingURL=627.bundle.js.map
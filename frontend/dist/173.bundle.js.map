{"version": 3, "file": "173.bundle.js", "mappings": "4HAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,iBAATA,GAAqC,mBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAK,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,GAWLQ,EAAgB,CAACC,EAAKL,EAAKM,KAtBT,EAACD,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMI,cAAc,EAAMC,UAAU,EAAMF,UAAWD,EAAIL,GAAOM,GAuBxJG,CAAgBJ,EAAoB,iBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAgB,CAAC,EAzBN,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf5B,EAAU2B,EAAQE,EAAM,CAAEX,IAAKU,EAAIC,GAAOV,YAAY,KAwB1DW,CAASJ,EAAe,CACtBK,QAAS,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBU,EAACvC,EAAKwC,EAAYT,KAAYA,EAAgB,MAAP/B,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,EAKpFf,GAAQA,EAAIyC,WAA8EV,EAAjE3B,EAAU2B,EAAQ,UAAW,CAAEL,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiB0C,CAAQ,EAAQ,OAC/BC,EAAe,EAAQ,MACvBC,EAAkB,EAAQ,KAC9B,MAEMC,EAAYC,GACTA,EAAIC,QAAQ,iBAAkB,IAEvC,MAAMX,UAAcG,EAAaS,UAC/B,WAAAC,GACEC,SAASC,WAET3B,EAAc4B,KAAM,aAAcT,EAAaU,YAC/C7B,EAAc4B,KAAM,WAAY,MAChC5B,EAAc4B,KAAM,cAAe,MACnC5B,EAAc4B,KAAM,gBAAiB,MACrC5B,EAAc4B,KAAM,OAAQ,KAC1BA,KAAKE,UAAS,KAEhB9B,EAAc4B,KAAM,SAAU,KAC5BA,KAAKE,UAAS,KAEhB9B,EAAc4B,KAAM,MAAQG,IAC1BH,KAAKG,UAAYA,GAErB,CACA,iBAAAC,GACEJ,KAAKK,MAAMC,SAAWN,KAAKK,MAAMC,QAAQN,KAC3C,CACA,IAAAO,CAAKb,GACHM,KAAKQ,SAAW,MAChB,EAAIjB,EAAakB,QA5BL,yCACG,SA2B+BC,KAAMC,IAClD,IAAKX,KAAKG,UACR,OACF,MAAM,cAAES,EAAa,MAAEC,GAAUb,KAAKK,MAAMS,OAC5Cd,KAAKe,OAAS,IAAIJ,EAAOK,OAAOhB,KAAKG,UAAW,CAC9CT,IAAKD,EAASC,GACduB,SAAUjB,KAAKK,MAAMa,QACrBC,MAAOnB,KAAKK,MAAMc,MAClBC,KAAMpB,KAAKK,MAAMe,KACjBC,YAAarB,KAAKK,MAAMgB,YACxBC,SAAUtB,KAAKK,MAAMiB,YAClBV,IAELZ,KAAKe,OAAOQ,QAAQb,KAAK,KACvB,MAAMc,EAASxB,KAAKG,UAAUsB,cAAc,UAC5CD,EAAOE,MAAMC,MAAQ,OACrBH,EAAOE,MAAME,OAAS,OAClBf,IACFW,EAAOX,MAAQA,KAEhBgB,MAAM7B,KAAKK,MAAMyB,SACpB9B,KAAKe,OAAOgB,GAAG,SAAU,KACvB/B,KAAKK,MAAM2B,UACXhC,KAAKiC,oBAEPjC,KAAKe,OAAOgB,GAAG,OAAQ,KACrB/B,KAAKK,MAAM6B,SACXlC,KAAKiC,oBAEPjC,KAAKe,OAAOgB,GAAG,QAAS/B,KAAKK,MAAM8B,SACnCnC,KAAKe,OAAOgB,GAAG,SAAWK,GAAMpC,KAAKK,MAAMgC,OAAOD,EAAEE,UACpDtC,KAAKe,OAAOgB,GAAG,QAAS/B,KAAKK,MAAMkC,SACnCvC,KAAKe,OAAOgB,GAAG,QAAS/B,KAAKK,MAAMyB,SACnC9B,KAAKe,OAAOgB,GAAG,aAAc,EAAGO,cAC9BtC,KAAKwC,YAAcF,IAErBtC,KAAKe,OAAOgB,GAAG,WAAY,EAAGO,cAC5BtC,KAAKyC,cAAgBH,IAEvBtC,KAAKe,OAAOgB,GAAG,cAAe/B,KAAKK,MAAMqC,UACzC1C,KAAKe,OAAOgB,GAAG,YAAa/B,KAAKK,MAAMsC,aACvC3C,KAAKe,OAAOgB,GAAG,qBAAuBK,GAAMpC,KAAKK,MAAMuC,qBAAqBR,EAAES,gBAC7E7C,KAAKK,MAAMyB,QAChB,CACA,eAAAG,GACEjC,KAAKe,OAAO+B,cAAcpC,KAAMF,IAC9BR,KAAKQ,SAAWA,GAEpB,CACA,IAAAuC,GACE,MAAMC,EAAUhD,KAAKC,WAAW,QAC5B+C,GACFA,EAAQnB,MAAM7B,KAAKK,MAAMyB,QAE7B,CACA,KAAAmB,GACEjD,KAAKC,WAAW,QAClB,CACA,IAAAiD,GACElD,KAAKC,WAAW,SAClB,CACA,MAAAkD,CAAOb,EAASc,GAAc,GAC5BpD,KAAKC,WAAW,iBAAkBqC,GAC7Bc,GACHpD,KAAKiD,OAET,CACA,SAAAI,CAAUC,GACRtD,KAAKC,WAAW,YAAaqD,EAC/B,CACA,QAAApD,CAASiB,GACPnB,KAAKC,WAAW,WAAYkB,EAC9B,CACA,OAAAoC,CAAQnC,GACNpB,KAAKC,WAAW,UAAWmB,EAC7B,CACA,eAAAoC,CAAgBC,GACdzD,KAAKC,WAAW,kBAAmBwD,EACrC,CACA,WAAAX,GACE,OAAO9C,KAAKQ,QACd,CACA,cAAAkD,GACE,OAAO1D,KAAKwC,WACd,CACA,gBAAAmB,GACE,OAAO3D,KAAKyC,aACd,CACA,MAAAmB,GACE,MAAM,QAAEC,GAAY7D,KAAKK,MACnBqB,EAAQ,CACZC,MAAO,OACPC,OAAQ,OACRkC,SAAU,SACVD,WAEF,OAAuB1E,EAAaJ,QAAQgF,cAC1C,MACA,CACE/F,IAAKgC,KAAKK,MAAMX,IAChBsE,IAAKhE,KAAKgE,IACVtC,SAGN,EAEFtD,EAAcY,EAAO,cAAe,SACpCZ,EAAcY,EAAO,UAAWQ,EAAgByE,QAAQC,OACxD9F,EAAcY,EAAO,aAAa,E", "sources": ["webpack://helios-subtitle-corrector/./node_modules/react-player/lib/players/Vimeo.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vimeo_exports = {};\n__export(Vimeo_exports, {\n  default: () => Vimeo\n});\nmodule.exports = __toCommonJS(Vimeo_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.vimeo.com/api/player.js\";\nconst SDK_GLOBAL = \"Vimeo\";\nconst cleanUrl = (url) => {\n  return url.replace(\"/manage/videos\", \"\");\n};\nclass Vimeo extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Prevent checking isLoading when URL changes\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setMuted(true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.setMuted(false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    this.duration = null;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Vimeo2) => {\n      if (!this.container)\n        return;\n      const { playerOptions, title } = this.props.config;\n      this.player = new Vimeo2.Player(this.container, {\n        url: cleanUrl(url),\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        loop: this.props.loop,\n        playsinline: this.props.playsinline,\n        controls: this.props.controls,\n        ...playerOptions\n      });\n      this.player.ready().then(() => {\n        const iframe = this.container.querySelector(\"iframe\");\n        iframe.style.width = \"100%\";\n        iframe.style.height = \"100%\";\n        if (title) {\n          iframe.title = title;\n        }\n      }).catch(this.props.onError);\n      this.player.on(\"loaded\", () => {\n        this.props.onReady();\n        this.refreshDuration();\n      });\n      this.player.on(\"play\", () => {\n        this.props.onPlay();\n        this.refreshDuration();\n      });\n      this.player.on(\"pause\", this.props.onPause);\n      this.player.on(\"seeked\", (e) => this.props.onSeek(e.seconds));\n      this.player.on(\"ended\", this.props.onEnded);\n      this.player.on(\"error\", this.props.onError);\n      this.player.on(\"timeupdate\", ({ seconds }) => {\n        this.currentTime = seconds;\n      });\n      this.player.on(\"progress\", ({ seconds }) => {\n        this.secondsLoaded = seconds;\n      });\n      this.player.on(\"bufferstart\", this.props.onBuffer);\n      this.player.on(\"bufferend\", this.props.onBufferEnd);\n      this.player.on(\"playbackratechange\", (e) => this.props.onPlaybackRateChange(e.playbackRate));\n    }, this.props.onError);\n  }\n  refreshDuration() {\n    this.player.getDuration().then((duration) => {\n      this.duration = duration;\n    });\n  }\n  play() {\n    const promise = this.callPlayer(\"play\");\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"unload\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setMuted(muted) {\n    this.callPlayer(\"setMuted\", muted);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      overflow: \"hidden\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        key: this.props.url,\n        ref: this.ref,\n        style\n      }\n    );\n  }\n}\n__publicField(Vimeo, \"displayName\", \"Vimeo\");\n__publicField(Vimeo, \"canPlay\", import_patterns.canPlay.vimeo);\n__publicField(Vimeo, \"forceLoad\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "configurable", "writable", "__defNormalProp", "Vimeo_exports", "target", "all", "name", "__export", "default", "Vimeo", "module", "exports", "import_react", "isNodeMode", "__esModule", "__toESM", "import_utils", "import_patterns", "cleanUrl", "url", "replace", "Component", "constructor", "super", "arguments", "this", "callPlayer", "setMuted", "container", "componentDidMount", "props", "onMount", "load", "duration", "getSDK", "then", "Vimeo2", "playerOptions", "title", "config", "player", "Player", "autoplay", "playing", "muted", "loop", "playsinline", "controls", "ready", "iframe", "querySelector", "style", "width", "height", "catch", "onError", "on", "onReady", "refreshDuration", "onPlay", "onPause", "e", "onSeek", "seconds", "onEnded", "currentTime", "secondsLoaded", "onBuffer", "onBufferEnd", "onPlaybackRateChange", "playbackRate", "getDuration", "play", "promise", "pause", "stop", "seekTo", "keepPlaying", "setVolume", "fraction", "setLoop", "setPlaybackRate", "rate", "getCurrentTime", "getSecondsLoaded", "render", "display", "overflow", "createElement", "ref", "canPlay", "vimeo"], "sourceRoot": ""}
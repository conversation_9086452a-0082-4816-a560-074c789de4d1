"""Correction suggestion schemas for request/response validation."""

from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime


class CorrectionSuggestionBase(BaseModel):
    """Base correction suggestion schema."""
    segment_id: int
    original_text: str = Field(..., min_length=1)
    suggested_text: str = Field(..., min_length=1)
    correction_type: str = Field(..., regex="^(spelling|grammar|punctuation|semantic|formatting)$")
    confidence_score: float = Field(..., ge=0, le=1)
    explanation: Optional[str] = None
    is_accepted: bool = False
    is_rejected: bool = False
    ai_model: str = Field(..., max_length=50)
    ai_provider: str = Field(..., max_length=50)


class CorrectionSuggestionCreate(CorrectionSuggestionBase):
    """Schema for creating a new correction suggestion."""
    pass


class CorrectionSuggestionUpdate(BaseModel):
    """Schema for updating correction suggestion."""
    suggested_text: Optional[str] = None
    explanation: Optional[str] = None
    is_accepted: Optional[bool] = None
    is_rejected: Optional[bool] = None
    confidence_score: Optional[float] = Field(None, ge=0, le=1)


class CorrectionSuggestionResponse(CorrectionSuggestionBase):
    """Schema for correction suggestion response."""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CorrectionSuggestionList(BaseModel):
    """Schema for correction suggestion list response."""
    items: list[CorrectionSuggestionResponse]
    total: int
    segment_id: int


class CorrectionBatchRequest(BaseModel):
    """Schema for batch correction request."""
    segment_ids: list[int] = Field(..., min_items=1)
    correction_types: Optional[list[str]] = None
    confidence_threshold: float = Field(default=0.7, ge=0, le=1)


class CorrectionBatchResponse(BaseModel):
    """Schema for batch correction response."""
    processed_segments: int
    suggestions_generated: int
    corrections: list[CorrectionSuggestionResponse]


class CorrectionStats(BaseModel):
    """Schema for correction statistics."""
    total_suggestions: int
    accepted_suggestions: int
    rejected_suggestions: int
    pending_suggestions: int
    by_type: dict[str, int]
    by_model: dict[str, int]
    average_confidence: float
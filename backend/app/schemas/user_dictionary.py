"""User dictionary schemas for request/response validation."""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class UserDictionaryBase(BaseModel):
    """Base user dictionary schema."""
    user_id: int
    word: str = Field(..., min_length=1, max_length=100)
    replacement: Optional[str] = Field(None, max_length=100)
    is_case_sensitive: bool = False
    is_regex: bool = False
    description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, max_length=50)
    is_active: bool = True


class UserDictionaryCreate(UserDictionaryBase):
    """Schema for creating a new dictionary entry."""
    pass


class UserDictionaryUpdate(BaseModel):
    """Schema for updating dictionary entry."""
    word: Optional[str] = Field(None, min_length=1, max_length=100)
    replacement: Optional[str] = Field(None, max_length=100)
    is_case_sensitive: Optional[bool] = None
    is_regex: Optional[bool] = None
    description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None


class UserDictionaryResponse(UserDictionaryBase):
    """Schema for dictionary entry response."""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserDictionaryList(BaseModel):
    """Schema for dictionary list response."""
    items: list[UserDictionaryResponse]
    total: int
    user_id: int


class DictionarySearch(BaseModel):
    """Schema for dictionary search."""
    query: Optional[str] = None
    category: Optional[str] = None
    is_active: Optional[bool] = None
    limit: int = Field(default=50, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class DictionaryImport(BaseModel):
    """Schema for dictionary import."""
    entries: list[UserDictionaryCreate]
    overwrite_existing: bool = False


class DictionaryExport(BaseModel):
    """Schema for dictionary export."""
    entries: list[UserDictionaryResponse]
    export_format: str = Field(default="json", regex="^(json|csv)$")
    include_inactive: bool = False


class DictionaryStats(BaseModel):
    """Schema for dictionary statistics."""
    total_entries: int
    active_entries: int
    inactive_entries: int
    by_category: dict[str, int]
    by_type: dict[str, int]  # case_sensitive, regex, etc.
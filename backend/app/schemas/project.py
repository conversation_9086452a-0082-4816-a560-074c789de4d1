"""Project schemas for request/response validation."""

from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List
from datetime import datetime
from enum import Enum


class ProjectStatus(str, Enum):
    """Project status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProjectBase(BaseModel):
    """Base project schema."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    youtube_url: HttpUrl = Field(..., description="YouTube video URL")
    original_language: str = Field(..., min_length=2, max_length=10)
    target_language: Optional[str] = Field(None, min_length=2, max_length=10)


class ProjectCreate(BaseModel):
    """Schema for creating a new project."""
    youtube_url: HttpUrl = Field(..., description="YouTube video URL")
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)


class ProjectUpdate(BaseModel):
    """Schema for updating project details."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[ProjectStatus] = None
    progress: Optional[float] = Field(None, ge=0, le=100)


class ProjectResponse(ProjectBase):
    """Schema for project response."""
    id: int
    user_id: int
    status: ProjectStatus
    progress: float = 0.0
    video_title: Optional[str] = None
    video_duration: Optional[int] = None
    thumbnail_url: Optional[str] = None
    original_subtitle_path: Optional[str] = None
    corrected_subtitle_path: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProjectList(BaseModel):
    """Schema for paginated project list."""
    items: List[ProjectResponse]
    total: int
    page: int
    size: int
    pages: int


class ProjectSummary(BaseModel):
    """Schema for project summary statistics."""
    total_projects: int
    completed_projects: int
    processing_projects: int
    failed_projects: int


# Aliases for compatibility
ProjectSearch = BaseModel
ProjectStats = ProjectSummary
ProjectExport = BaseModel
ProjectImport = BaseModel
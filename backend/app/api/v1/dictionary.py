"""User dictionary management endpoints for Helios subtitle corrector."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.user_dictionary import UserDictionary
from app.schemas.user_dictionary import (
    UserDictionaryCreate,
    UserDictionaryResponse,
    UserDictionaryUpdate
)

router = APIRouter(prefix="/dictionary", tags=["dictionary"])


@router.post("/", response_model=UserDictionaryResponse)
async def create_dictionary_entry(
    entry: UserDictionaryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new user dictionary entry."""
    # Check if entry already exists
    existing = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id,
        UserDictionary.original_word == entry.original_word
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail="Dictionary entry for this word already exists"
        )
    
    db_entry = UserDictionary(
        user_id=current_user.id,
        original_word=entry.original_word,
        corrected_word=entry.corrected_word,
        context=entry.context,
        notes=entry.notes
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    return db_entry


@router.get("/", response_model=List[UserDictionaryResponse])
async def get_dictionary_entries(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's dictionary entries with optional search."""
    query = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id
    )
    
    if search:
        query = query.filter(
            UserDictionary.original_word.contains(search) |
            UserDictionary.corrected_word.contains(search) |
            UserDictionary.context.contains(search)
        )
    
    entries = query.order_by(UserDictionary.original_word).offset(skip).limit(limit).all()
    return entries


@router.get("/{entry_id}", response_model=UserDictionaryResponse)
async def get_dictionary_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    return entry


@router.put("/{entry_id}", response_model=UserDictionaryResponse)
async def update_dictionary_entry(
    entry_id: int,
    entry_update: UserDictionaryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    update_data = entry_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(entry, field, value)
    
    db.commit()
    db.refresh(entry)
    return entry


@router.delete("/{entry_id}")
async def delete_dictionary_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    db.delete(entry)
    db.commit()
    
    return {"message": "Dictionary entry deleted successfully"}


@router.get("/search/{word}")
async def search_dictionary(
    word: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search for a word in the dictionary."""
    entries = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id,
        UserDictionary.original_word == word
    ).all()
    
    return {
        "word": word,
        "entries": entries,
        "has_correction": len(entries) > 0
    }


@router.post("/bulk-import")
async def bulk_import_dictionary(
    entries: List[UserDictionaryCreate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Import multiple dictionary entries at once."""
    created_entries = []
    
    for entry in entries:
        # Skip duplicates
        existing = db.query(UserDictionary).filter(
            UserDictionary.user_id == current_user.id,
            UserDictionary.original_word == entry.original_word
        ).first()
        
        if existing:
            continue
        
        db_entry = UserDictionary(
            user_id=current_user.id,
            original_word=entry.original_word,
            corrected_word=entry.corrected_word,
            context=entry.context,
            notes=entry.notes
        )
        
        db.add(db_entry)
        created_entries.append(db_entry)
    
    db.commit()
    
    for entry in created_entries:
        db.refresh(entry)
    
    return {
        "imported_count": len(created_entries),
        "entries": created_entries
    }


@router.get("/export")
async def export_dictionary(
    format: str = Query("json", regex="^(json|csv)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export dictionary entries."""
    entries = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id
    ).order_by(UserDictionary.original_word).all()
    
    if format == "json":
        return {
            "format": "json",
            "entries": entries
        }
    
    elif format == "csv":
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow(["original_word", "corrected_word", "context", "notes"])
        
        # Write entries
        for entry in entries:
            writer.writerow([
                entry.original_word,
                entry.corrected_word,
                entry.context or "",
                entry.notes or ""
            ])
        
        return {
            "format": "csv",
            "content": output.getvalue(),
            "filename": "helios_dictionary.csv"
        }
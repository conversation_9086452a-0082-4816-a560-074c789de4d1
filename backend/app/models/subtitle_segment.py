from sqlalchemy import Column, Integer, String, DateTime, Text, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class SubtitleSegment(Base):
    __tablename__ = "subtitle_segments"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    segment_index = Column(Integer, nullable=False)
    start_time = Column(Float, nullable=False)
    end_time = Column(Float, nullable=False)
    original_text = Column(Text, nullable=False)
    corrected_text = Column(Text)
    confidence_score = Column(Float)
    speaker_id = Column(String(50))
    is_processed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="subtitle_segments")
    correction_suggestions = relationship("CorrectionSuggestion", back_populates="subtitle_segment", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SubtitleSegment(id={self.id}, project_id={self.project_id}, index={self.segment_index})>"